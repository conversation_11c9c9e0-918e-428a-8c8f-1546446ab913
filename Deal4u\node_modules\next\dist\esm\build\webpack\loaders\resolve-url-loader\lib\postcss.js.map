{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/postcss.ts"], "names": ["path", "prepend", "remove", "ORPHAN_CR_REGEX", "process", "postcss", "sourceFile", "sourceContent", "params", "plugin", "postcssPlugin", "from", "map", "outputSourceMap", "prev", "inputSourceMap", "inline", "annotation", "sourcesContent", "then", "result", "content", "css", "toJSON", "styles", "walkDecls", "eachDeclaration", "declaration", "<PERSON><PERSON><PERSON><PERSON>", "value", "indexOf", "startPosApparent", "source", "start", "startPosOriginal", "sourceMapConsumer", "originalPositionFor", "directory", "dirname", "transformDeclaration", "Error", "test"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA,OAAOA,UAAU,OAAM;AACvB,SAASC,OAAO,EAAEC,MAAM,QAAQ,kBAAiB;AAEjD,MAAMC,kBAAkB;AAExB,eAAe,SAASC,QACtBC,OAAY,EACZC,UAAe,EACfC,aAAkB,EAClBC,MAAW;IAEX,sHAAsH;IAEtH,yEAAyE;IACzE,mEAAmE;IACnE,OAAOH,QAAQ;QAACA,QAAQI,MAAM,CAAC,uBAAuBC;KAAe,EAClEN,OAAO,CAACG,eAAe;QACtBI,MAAMV,QAAQK;QACdM,KAAKJ,OAAOK,eAAe,IAAI;YAC7BC,MAAM,CAAC,CAACN,OAAOO,cAAc,IAAId,QAAQO,OAAOO,cAAc;YAC9DC,QAAQ;YACRC,YAAY;YACZC,gBAAgB;QAClB;IACF,GACCC,IAAI,CAAC,CAACC,SAAiB,CAAA;YACtBC,SAASD,OAAOE,GAAG;YACnBV,KAAKJ,OAAOK,eAAe,GAAGX,OAAOkB,OAAOR,GAAG,CAACW,MAAM,MAAM;QAC9D,CAAA;IAEF;;GAEC,GACD,SAASb;QACP,OAAO,SAAUc,MAAW;YAC1B,mEAAmE;YACnEA,OAAOC,SAAS,CAACC;QACnB;QAEA;;;KAGC,GACD,SAASA,gBAAgBC,WAAgB;YACvC,MAAMC,UAAUD,YAAYE,KAAK,IAAIF,YAAYE,KAAK,CAACC,OAAO,CAAC,UAAU;YACzE,IAAIF,SAAS;gBACX,wFAAwF;gBACxF,MAAMG,mBAAmBJ,YAAYK,MAAM,CAACC,KAAK,EAC/CC,mBACE1B,OAAO2B,iBAAiB,IACxB3B,OAAO2B,iBAAiB,CAACC,mBAAmB,CAACL;gBAEjD,sDAAsD;gBACtD,MAAMM,YACJH,oBACAA,iBAAiBF,MAAM,IACvB9B,OAAOF,KAAKsC,OAAO,CAACJ,iBAAiBF,MAAM;gBAC7C,IAAIK,WAAW;oBACbV,YAAYE,KAAK,GAAGrB,OAAO+B,oBAAoB,CAC7CZ,YAAYE,KAAK,EACjBQ;gBAEJ,OAEK,IAAI7B,OAAO2B,iBAAiB,EAAE;oBACjC,MAAM,IAAIK,MACR,kEACGrC,CAAAA,gBAAgBsC,IAAI,CAAClC,iBAClB,2CACA,sBAAqB;gBAE/B;YACF;QACF;IACF;AACF"}