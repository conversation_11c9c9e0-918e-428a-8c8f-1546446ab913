<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ Deal4U Master Control Panel</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
    <style>
        body {
            margin: 0;
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .master-control-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            color: white;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .header-title {
            font-size: 3rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .header-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .header-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>

    <div class="master-control-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-content">
                <div>
                    <h1 class="header-title">🎛️ Deal4U Master Control Panel</h1>
                    <p class="header-subtitle">All your admin features in one place - Import, Sync, Categorize & Manage</p>
                </div>
                <div class="header-actions">
                    <a href="/" class="header-btn">
                        <i class="fas fa-home"></i> View Store
                    </a>
                    <button class="header-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Actions Grid -->
        <div class="actions-grid">
            <!-- Fast WordPress Sync -->
            <div class="action-card sync-card">
                <div class="card-header">
                    <div class="card-icon sync-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h2>🚀 Fast WordPress Sync</h2>
                </div>
                <p class="card-description">
                    Ultra-fast sync: Publish all WordPress products in seconds with real images
                </p>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="wordpress-products">0</span>
                        <span class="stat-label">WordPress Products</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="website-products">0</span>
                        <span class="stat-label">Website Products</span>
                    </div>
                </div>
                <button class="action-button primary" onclick="fastSync()">
                    <i class="fas fa-rocket"></i>
                    FAST SYNC (Seconds!)
                </button>
            </div>

            <!-- Product Import -->
            <div class="action-card import-card">
                <div class="card-header">
                    <div class="card-icon import-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <h2>📦 Product Import</h2>
                </div>
                <p class="card-description">
                    Import products manually using your AliDrop plugin, then use Fast Sync to optimize them
                </p>
                <div class="import-process">
                    <h3>📋 Manual Import Process:</h3>
                    <ol>
                        <li>Use your AliDrop plugin to import products</li>
                        <li>Click "🚀 FAST SYNC" to optimize images and publish</li>
                        <li>Products will appear in your shop with clean data</li>
                    </ol>
                </div>
                <button class="action-button secondary" onclick="openImportGuide()">
                    <i class="fas fa-book"></i>
                    Import Guide
                </button>
            </div>

            <!-- Smart Categories -->
            <div class="action-card categories-card">
                <div class="card-header">
                    <div class="card-icon categories-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h2>🧠 Smart Categories</h2>
                </div>
                <p class="card-description">
                    AI-powered product categorization with intelligent keyword detection
                </p>
                <div class="categories-info">
                    <div class="category-tag">👗 Woman Clothes</div>
                    <div class="category-tag">📱 Electronics</div>
                    <div class="category-tag">💎 Accessories</div>
                </div>
                <button class="action-button tertiary" onclick="analyzeCategories()">
                    <i class="fas fa-brain"></i>
                    Analyze Categories
                </button>
            </div>
        </div>

        <!-- System Status Section -->
        <div class="status-section">
            <div class="status-card">
                <h2>📊 System Status</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-number" id="total-products-count">0</div>
                        <div class="status-label">Total Products</div>
                        <div class="status-indicator active"></div>
                    </div>
                    <div class="status-item">
                        <div class="status-number" id="published-products-count">0</div>
                        <div class="status-label">Published Products</div>
                        <div class="status-indicator active"></div>
                    </div>
                    <div class="status-item">
                        <div class="status-number" id="sync-status">Stopped</div>
                        <div class="status-label">Auto-Sync Status</div>
                        <div class="status-indicator inactive"></div>
                    </div>
                </div>
            </div>

            <div class="api-status-card">
                <h3>🔗 API Connection Status</h3>
                <div class="api-status-list">
                    <div class="api-status-item">
                        <span>WooCommerce API</span>
                        <span class="status-badge" id="api-status">Checking...</span>
                    </div>
                    <div class="api-status-item">
                        <span>Cache Status</span>
                        <span class="status-badge active" id="cache-status">Active</span>
                    </div>
                    <div class="api-status-item">
                        <span>Last Sync</span>
                        <span class="status-badge" id="last-sync">Never</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Tools Section -->
        <div class="tools-section">
            <div class="tools-grid">
                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3>⚙️ Settings</h3>
                    <p>Configure WooCommerce API, site settings, and cache management</p>
                    <button class="tool-button" onclick="openSettings()">
                        <i class="fas fa-cog"></i> Open Settings
                    </button>
                </div>

                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>📈 Analytics</h3>
                    <p>View product performance, sync logs, and system analytics</p>
                    <button class="tool-button" onclick="openAnalytics()">
                        <i class="fas fa-chart-bar"></i> View Analytics
                    </button>
                </div>

                <div class="tool-card">
                    <div class="tool-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>🗄️ Data Management</h3>
                    <p>Export data, clear cache, and manage product database</p>
                    <button class="tool-button" onclick="openDataManagement()">
                        <i class="fas fa-database"></i> Manage Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Sync Log Section -->
        <div class="log-section">
            <div class="log-card">
                <h2>📋 Sync Activity Log</h2>
                <div class="log-controls">
                    <button class="log-btn" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> Clear Logs
                    </button>
                    <button class="log-btn" onclick="exportLogs()">
                        <i class="fas fa-download"></i> Export Logs
                    </button>
                    <button class="log-btn" onclick="refreshLogs()">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>
                <div id="sync-log-content" class="log-content">
                    <div class="log-entry">
                        <span class="log-time">Ready</span>
                        <span class="log-message">System initialized and ready for operations</span>
                        <span class="log-status success">✅</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ Settings</h2>
                <button class="modal-close" onclick="closeSettings()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-tabs">
                    <button class="tab-btn active" onclick="switchTab('api')">API Settings</button>
                    <button class="tab-btn" onclick="switchTab('site')">Site Config</button>
                    <button class="tab-btn" onclick="switchTab('cache')">Cache</button>
                </div>

                <div id="api-tab" class="tab-content active">
                    <h3>WooCommerce API Configuration</h3>
                    <form onsubmit="saveApiSettings(event)">
                        <div class="form-group">
                            <label>Base URL</label>
                            <input type="url" id="api-base-url" placeholder="https://your-site.com/wp-json/wc/v3">
                        </div>
                        <div class="form-group">
                            <label>Consumer Key</label>
                            <input type="text" id="api-consumer-key" placeholder="ck_...">
                        </div>
                        <div class="form-group">
                            <label>Consumer Secret</label>
                            <input type="password" id="api-consumer-secret" placeholder="cs_...">
                        </div>
                        <button type="submit" class="settings-btn">Save API Settings</button>
                    </form>
                </div>

                <div id="site-tab" class="tab-content">
                    <h3>Site Configuration</h3>
                    <form onsubmit="saveSiteSettings(event)">
                        <div class="form-group">
                            <label>Site Name</label>
                            <input type="text" id="site-name" value="Deal4u">
                        </div>
                        <div class="form-group">
                            <label>Contact Email</label>
                            <input type="email" id="contact-email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>Phone Number</label>
                            <input type="tel" id="phone-number" value="+447447186806">
                        </div>
                        <button type="submit" class="settings-btn">Save Site Settings</button>
                    </form>
                </div>

                <div id="cache-tab" class="tab-content">
                    <h3>Cache Management</h3>
                    <div class="cache-controls">
                        <button class="settings-btn" onclick="clearAllCache()">Clear All Cache</button>
                        <button class="settings-btn" onclick="preloadCache()">Preload Cache</button>
                        <div class="cache-info">
                            <p>Cache Size: <span id="cache-size">0 KB</span></p>
                            <p>Last Cleared: <span id="cache-cleared">Never</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/main.js"></script>

    <script>
        // Master Control Panel JavaScript

        // Utility functions fallback
        function showToast(message, type = 'info') {
            console.log(`${type.toUpperCase()}: ${message}`);
            alert(message);
        }

        function clearLogs() {
            const logContent = document.getElementById('sync-log-content');
            if (logContent) {
                logContent.innerHTML = '<div class="log-entry"><span class="log-time">Ready</span><span class="log-message">Logs cleared</span><span class="log-status success">✅</span></div>';
            }
        }

        function exportLogs() {
            alert('📋 Export Logs feature - Coming soon!');
        }

        function refreshLogs() {
            addLogEntry('Logs refreshed', 'info');
        }

        function clearAllCache() {
            alert('🗄️ Cache cleared successfully!');
            addLogEntry('Cache cleared', 'success');
        }

        function preloadCache() {
            alert('🗄️ Cache preloading started!');
            addLogEntry('Cache preloading started', 'info');
        }

        function saveApiSettings(event) {
            event.preventDefault();
            alert('⚙️ API settings saved (demo mode)');
            addLogEntry('API settings updated', 'success');
        }

        function saveSiteSettings(event) {
            event.preventDefault();
            alert('⚙️ Site settings saved (demo mode)');
            addLogEntry('Site settings updated', 'success');
        }

        // Fast Sync Function
        async function fastSync() {
            if (confirm('🚀 FAST SYNC: Publish all your WordPress products in seconds?')) {
                const syncBtn = event.target;
                const originalText = syncBtn.innerHTML;

                try {
                    syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
                    syncBtn.disabled = true;

                    const startTime = Date.now();

                    // Simulate API call - replace with actual API endpoint
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    const elapsed = Date.now() - startTime;
                    const mockData = {
                        success: true,
                        published: 34,
                        timeElapsed: `${(elapsed/1000).toFixed(1)}s`,
                        speed: 'Ultra-Fast'
                    };

                    if (mockData.success) {
                        alert(`🎉 ULTRA-FAST SYNC COMPLETE!\n\n✅ ${mockData.published} products published\n⚡ Completed in ${mockData.timeElapsed}\n🚀 Speed: ${mockData.speed}\n\nYour products are now live on the website!`);
                        updateProductCounts();
                        addLogEntry('Fast Sync completed successfully', 'success');
                    }
                } catch (error) {
                    alert('❌ Error: ' + error.message);
                    addLogEntry('Fast Sync failed: ' + error.message, 'error');
                } finally {
                    syncBtn.innerHTML = originalText;
                    syncBtn.disabled = false;
                }
            }
        }

        // Other Functions
        function openImportGuide() {
            alert('📦 Import Guide:\n\n1. Use your AliDrop plugin to import products\n2. Click "🚀 FAST SYNC" to optimize images and publish\n3. Products will appear in your shop with clean data');
        }

        function analyzeCategories() {
            alert('🧠 Smart Categories feature - Analyzing product categories with AI...');
            addLogEntry('Smart categorization analysis started', 'info');
        }

        function openSettings() {
            document.getElementById('settings-modal').style.display = 'flex';
        }

        function closeSettings() {
            document.getElementById('settings-modal').style.display = 'none';
        }

        function openAnalytics() {
            alert('📈 Analytics feature - Coming soon!');
        }

        function openDataManagement() {
            alert('🗄️ Data Management feature - Coming soon!');
        }

        function switchTab(tabName) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to selected tab
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
        }

        function addLogEntry(message, type = 'info') {
            const logContent = document.getElementById('sync-log-content');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';

            const time = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';

            logEntry.innerHTML = `
                <span class="log-time">${time}</span>
                <span class="log-message">${message}</span>
                <span class="log-status ${type}">${icon}</span>
            `;

            logContent.insertBefore(logEntry, logContent.firstChild);
        }

        function updateProductCounts() {
            // Update product counts - replace with actual API calls
            document.getElementById('total-products-count').textContent = '34';
            document.getElementById('published-products-count').textContent = '34';
            document.getElementById('wordpress-products').textContent = '34';
            document.getElementById('website-products').textContent = '34';
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                sessionStorage.removeItem('admin_authenticated');
                window.location.href = '/';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateProductCounts();
            addLogEntry('Master Control Panel initialized', 'success');
        });
    </script>

    <style>
        /* Actions Grid */
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .action-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .sync-icon {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .import-icon {
            background: linear-gradient(135deg, #4834d4, #686de0);
        }

        .categories-icon {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
        }

        .card-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .card-description {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .card-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 12px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .action-button {
            width: 100%;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .action-button.primary {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .action-button.primary:hover {
            background: linear-gradient(135deg, #ee5a24, #ff6b6b);
            transform: translateY(-2px);
        }

        .action-button.secondary {
            background: linear-gradient(135deg, #4834d4, #686de0);
            color: white;
        }

        .action-button.secondary:hover {
            background: linear-gradient(135deg, #686de0, #4834d4);
            transform: translateY(-2px);
        }

        .action-button.tertiary {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
        }

        .action-button.tertiary:hover {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            transform: translateY(-2px);
        }

        .import-process {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .import-process h3 {
            margin: 0 0 0.5rem 0;
            color: #1e40af;
            font-size: 1rem;
        }

        .import-process ol {
            margin: 0;
            padding-left: 1.5rem;
            color: #1e40af;
        }

        .import-process li {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }

        .categories-info {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .category-tag {
            background: #f3e8ff;
            color: #7c3aed;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        /* Status Section */
        .status-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .status-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .status-card h2 {
            margin: 0 0 1.5rem 0;
            color: #1f2937;
            font-size: 1.5rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .status-item {
            text-align: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 12px;
            position: relative;
        }

        .status-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            display: block;
        }

        .status-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .status-indicator {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-indicator.active {
            background: #10b981;
        }

        .status-indicator.inactive {
            background: #ef4444;
        }

        .api-status-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .api-status-card h3 {
            margin: 0 0 1rem 0;
            color: #1f2937;
        }

        .api-status-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .api-status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            background: #10b981;
            color: white;
        }

        .status-badge.active {
            background: #10b981;
        }

        .status-badge.error {
            background: #ef4444;
        }

        .status-badge.warning {
            background: #f59e0b;
        }

        /* Tools Section */
        .tools-section {
            margin-bottom: 3rem;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .tool-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .tool-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
            color: white;
            font-size: 1.5rem;
        }

        .tool-card h3 {
            margin: 0 0 1rem 0;
            color: #1f2937;
            font-size: 1.2rem;
        }

        .tool-card p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .tool-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tool-button:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
            transform: translateY(-2px);
        }

        /* Log Section */
        .log-section {
            margin-bottom: 3rem;
        }

        .log-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .log-card h2 {
            margin: 0 0 1rem 0;
            color: #1f2937;
            font-size: 1.5rem;
        }

        .log-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .log-btn {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .log-btn:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        .log-content {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            background: #f8fafc;
        }

        .log-entry {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }

        .log-entry:last-child {
            margin-bottom: 0;
        }

        .log-time {
            font-size: 0.8rem;
            color: #6b7280;
            min-width: 80px;
        }

        .log-message {
            flex: 1;
            color: #374151;
            font-size: 0.9rem;
        }

        .log-status {
            font-size: 1rem;
        }

        .log-status.success {
            color: #10b981;
        }

        .log-status.error {
            color: #ef4444;
        }

        .log-status.info {
            color: #3b82f6;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 2rem 1rem 2rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h2 {
            margin: 0;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 2rem;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 2rem;
        }

        .settings-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            cursor: pointer;
            color: #6b7280;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-btn:hover {
            color: #374151;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #374151;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .settings-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .settings-btn:hover {
            background: #5a67d8;
        }

        .cache-controls {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .cache-info {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .cache-info p {
            margin: 0.5rem 0;
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .master-control-container {
                padding: 1rem;
            }

            .header-section {
                padding: 2rem;
            }

            .header-title {
                font-size: 2rem;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .status-section {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }

            .tools-grid {
                grid-template-columns: 1fr;
            }

            .log-controls {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .settings-tabs {
                flex-direction: column;
            }
        }
    </style>
</body>
</html>
