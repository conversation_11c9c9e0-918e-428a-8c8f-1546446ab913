{"version": 3, "sources": ["../../../src/client/components/navigation.ts"], "names": ["useContext", "useMemo", "AppRouterContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "clientHookInServerComponentError", "getSegmentValue", "INTERNAL_URLSEARCHPARAMS_INSTANCE", "Symbol", "readonlyURLSearchParamsError", "Error", "ReadonlyURLSearchParams", "iterator", "append", "delete", "set", "sort", "constructor", "urlSearchParams", "entries", "bind", "for<PERSON>ach", "get", "getAll", "has", "keys", "values", "toString", "size", "useSearchParams", "searchParams", "readonlySearchParams", "window", "bailoutToClientRendering", "require", "usePathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "getSelectedParams", "tree", "params", "parallelRoutes", "parallelRoute", "Object", "segment", "isDynamicParameter", "Array", "isArray", "segmentValue", "startsWith", "isCatchAll", "split", "useParams", "globalLayoutRouter", "pathParams", "getSelectedLayoutSegmentPath", "parallelRouteKey", "first", "segmentPath", "node", "children", "push", "useSelectedLayoutSegments", "useSelectedLayoutSegment", "selectedLayoutSegments", "length", "redirect", "permanentRedirect", "RedirectType", "notFound"], "mappings": "AAAA,SAASA,UAAU,EAAEC,OAAO,QAAQ,QAAO;AAE3C,SACEC,gBAAgB,EAChBC,yBAAyB,EACzBC,mBAAmB,QACd,qDAAoD;AAC3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,gCAAgC,QAAQ,0CAAyC;AAC1F,SAASC,eAAe,QAAQ,8CAA6C;AAE7E,MAAMC,oCAAoCC,OACxC;AAGF,SAASC;IACP,OAAO,IAAIC,MAAM;AACnB;AAEA,OAAO,MAAMC;IA0BX,CAACH,OAAOI,QAAQ,CAAC,GAAG;QAClB,OAAO,IAAI,CAACL,kCAAkC,CAACC,OAAOI,QAAQ,CAAC;IACjE;IAEAC,SAAS;QACP,MAAMJ;IACR;IACAK,SAAS;QACP,MAAML;IACR;IACAM,MAAM;QACJ,MAAMN;IACR;IACAO,OAAO;QACL,MAAMP;IACR;IA5BAQ,YAAYC,eAAgC,CAAE;QAC5C,IAAI,CAACX,kCAAkC,GAAGW;QAE1C,IAAI,CAACC,OAAO,GAAGD,gBAAgBC,OAAO,CAACC,IAAI,CAACF;QAC5C,IAAI,CAACG,OAAO,GAAGH,gBAAgBG,OAAO,CAACD,IAAI,CAACF;QAC5C,IAAI,CAACI,GAAG,GAAGJ,gBAAgBI,GAAG,CAACF,IAAI,CAACF;QACpC,IAAI,CAACK,MAAM,GAAGL,gBAAgBK,MAAM,CAACH,IAAI,CAACF;QAC1C,IAAI,CAACM,GAAG,GAAGN,gBAAgBM,GAAG,CAACJ,IAAI,CAACF;QACpC,IAAI,CAACO,IAAI,GAAGP,gBAAgBO,IAAI,CAACL,IAAI,CAACF;QACtC,IAAI,CAACQ,MAAM,GAAGR,gBAAgBQ,MAAM,CAACN,IAAI,CAACF;QAC1C,IAAI,CAACS,QAAQ,GAAGT,gBAAgBS,QAAQ,CAACP,IAAI,CAACF;QAC9C,IAAI,CAACU,IAAI,GAAGV,gBAAgBU,IAAI;IAClC;AAiBF;AAEA;;;CAGC,GACD,OAAO,SAASC;IACdxB,iCAAiC;IACjC,MAAMyB,eAAejC,WAAWK;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAM6B,uBAAuBjC,QAAQ;QACnC,IAAI,CAACgC,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAInB,wBAAwBmB;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOE,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,wBAAwB,EAAE,GAChCC,QAAQ;QACV,IAAID,4BAA4B;YAC9B,mEAAmE;YACnE,OAAOF;QACT;IACF;IAEA,OAAOA;AACT;AAEA;;CAEC,GACD,OAAO,SAASI;IACd9B,iCAAiC;IACjC,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOR,WAAWM;AACpB;AAEA,SACEiC,yBAAyB,EACzBC,qBAAqB,QAChB,uDAAsD;AAE7D;;CAEC,GACD,OAAO,SAASC;IACdjC,iCAAiC;IACjC,MAAMkC,SAAS1C,WAAWE;IAC1B,IAAIwC,WAAW,MAAM;QACnB,MAAM,IAAI7B,MAAM;IAClB;IAEA,OAAO6B;AACT;AAMA,+EAA+E;AAC/E,SAAS;AACT,SAASC,kBACPC,IAAuB,EACvBC,MAAmB;IAAnBA,IAAAA,mBAAAA,SAAiB,CAAC;IAElB,MAAMC,iBAAiBF,IAAI,CAAC,EAAE;IAE9B,KAAK,MAAMG,iBAAiBC,OAAOnB,MAAM,CAACiB,gBAAiB;QACzD,MAAMG,UAAUF,aAAa,CAAC,EAAE;QAChC,MAAMG,qBAAqBC,MAAMC,OAAO,CAACH;QACzC,MAAMI,eAAeH,qBAAqBD,OAAO,CAAC,EAAE,GAAGA;QACvD,IAAI,CAACI,gBAAgBA,aAAaC,UAAU,CAAC,aAAa;QAE1D,iEAAiE;QACjE,MAAMC,aACJL,sBAAuBD,CAAAA,OAAO,CAAC,EAAE,KAAK,OAAOA,OAAO,CAAC,EAAE,KAAK,IAAG;QAEjE,IAAIM,YAAY;YACdV,MAAM,CAACI,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE,CAACO,KAAK,CAAC;QACxC,OAAO,IAAIN,oBAAoB;YAC7BL,MAAM,CAACI,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE;QACjC;QAEAJ,SAASF,kBAAkBI,eAAeF;IAC5C;IAEA,OAAOA;AACT;AAEA;;;CAGC,GACD,OAAO,SAASY;IACdjD,iCAAiC;IACjC,MAAMkD,qBAAqB1D,WAAWG;IACtC,MAAMwD,aAAa3D,WAAWO;IAE9B,OAAON,QAAQ;QACb,6BAA6B;QAC7B,IAAIyD,sCAAAA,mBAAoBd,IAAI,EAAE;YAC5B,OAAOD,kBAAkBe,mBAAmBd,IAAI;QAClD;QAEA,2CAA2C;QAC3C,OAAOe;IACT,GAAG;QAACD,sCAAAA,mBAAoBd,IAAI;QAAEe;KAAW;AAC3C;AAEA,mCAAmC;AACnC;;CAEC,GACD,SAASC,6BACPhB,IAAuB,EACvBiB,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,kBAAAA,QAAQ;IACRC,IAAAA,wBAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOpB,IAAI,CAAC,EAAE,CAACiB,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMf,iBAAiBF,IAAI,CAAC,EAAE;YACvBE;QAAPkB,OAAOlB,CAAAA,2BAAAA,eAAemB,QAAQ,YAAvBnB,2BAA2BE,OAAOnB,MAAM,CAACiB,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACkB,MAAM,OAAOD;IAClB,MAAMd,UAAUe,IAAI,CAAC,EAAE;IAEvB,MAAMX,eAAe5C,gBAAgBwC;IACrC,IAAI,CAACI,gBAAgBA,aAAaC,UAAU,CAAC,aAAa,OAAOS;IAEjEA,YAAYG,IAAI,CAACb;IAEjB,OAAOO,6BACLI,MACAH,kBACA,OACAE;AAEJ;AAEA,iEAAiE;AACjE;;CAEC,GACD,OAAO,SAASI,0BACdN,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3BrD,iCAAiC;IACjC,MAAM,EAAEoC,IAAI,EAAE,GAAG5C,WAAWI;IAC5B,OAAOwD,6BAA6BhB,MAAMiB;AAC5C;AAEA,iEAAiE;AACjE;;CAEC,GACD,OAAO,SAASO,yBACdP,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3BrD,iCAAiC;IACjC,MAAM6D,yBAAyBF,0BAA0BN;IACzD,IAAIQ,uBAAuBC,MAAM,KAAK,GAAG;QACvC,OAAO;IACT;IAEA,OAAOD,sBAAsB,CAAC,EAAE;AAClC;AAEA,SAASE,QAAQ,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,aAAY;AACtE,SAASC,QAAQ,QAAQ,cAAa"}