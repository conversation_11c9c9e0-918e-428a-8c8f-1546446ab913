{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/fast-refresh-reducer.ts"], "names": ["fetchServerResponse", "createRecordFromThenable", "readRecordValue", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "applyFlightData", "fastRefreshReducerImpl", "state", "action", "cache", "mutable", "origin", "href", "canonicalUrl", "isForCurrentTree", "JSON", "stringify", "previousTree", "tree", "data", "URL", "nextUrl", "buildId", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "canonicalUrlOverrideHref", "undefined", "applied", "patchedTree", "fastRefreshReducerNoop", "_action", "fastRefreshReducer", "process", "env", "NODE_ENV"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAMjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AAEtD,wFAAwF;AACxF,SAASC,uBACPC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGH;IACnC,MAAMI,OAAOL,MAAMM,YAAY;IAE/B,MAAMC,mBACJC,KAAKC,SAAS,CAACN,QAAQO,YAAY,MAAMF,KAAKC,SAAS,CAACT,MAAMW,IAAI;IAEpE,IAAIJ,kBAAkB;QACpB,OAAOV,cAAcG,OAAOG;IAC9B;IAEA,IAAI,CAACD,MAAMU,IAAI,EAAE;QACf,uDAAuD;QACvD,wCAAwC;QACxCV,MAAMU,IAAI,GAAGrB,yBACXD,oBACE,IAAIuB,IAAIR,MAAMD,SACd;YAACJ,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAE;SAAU,EACxDX,MAAMc,OAAO,EACbd,MAAMe,OAAO;IAGnB;IACA,MAAM,CAACC,YAAYC,qBAAqB,GAAGzB,gBAAgBU,MAAMU,IAAI;IAErE,4DAA4D;IAC5D,IAAI,OAAOI,eAAe,UAAU;QAClC,OAAOpB,kBACLI,OACAG,SACAa,YACAhB,MAAMkB,OAAO,CAACC,WAAW;IAE7B;IAEA,2DAA2D;IAC3DjB,MAAMU,IAAI,GAAG;IAEb,IAAIQ,cAAcpB,MAAMW,IAAI;IAC5B,IAAIU,eAAerB,MAAME,KAAK;IAE9B,KAAK,MAAMoB,kBAAkBN,WAAY;QACvC,oFAAoF;QACpF,IAAIM,eAAeC,MAAM,KAAK,GAAG;YAC/B,oCAAoC;YACpCC,QAAQC,GAAG,CAAC;YACZ,OAAOzB;QACT;QAEA,2GAA2G;QAC3G,MAAM,CAAC0B,UAAU,GAAGJ;QACpB,MAAMK,UAAUjC,4BACd,sBAAsB;QACtB;YAAC;SAAG,EACJ0B,aACAM;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIjC,4BAA4ByB,aAAaO,UAAU;YACrD,OAAO/B,kBAAkBI,OAAOG,SAASE,MAAML,MAAMkB,OAAO,CAACC,WAAW;QAC1E;QAEA,MAAMU,2BAA2BZ,uBAC7BxB,kBAAkBwB,wBAClBa;QAEJ,IAAIb,sBAAsB;YACxBd,QAAQG,YAAY,GAAGuB;QACzB;QACA,MAAME,UAAUjC,gBAAgBuB,cAAcnB,OAAOoB;QAErD,IAAIS,SAAS;YACX5B,QAAQD,KAAK,GAAGA;YAChBmB,eAAenB;QACjB;QAEAC,QAAQO,YAAY,GAAGU;QACvBjB,QAAQ6B,WAAW,GAAGL;QACtBxB,QAAQG,YAAY,GAAGD;QAEvBe,cAAcO;IAChB;IACA,OAAO9B,cAAcG,OAAOG;AAC9B;AAEA,SAAS8B,uBACPjC,KAA2B,EAC3BkC,OAA0B;IAE1B,OAAOlC;AACT;AAEA,OAAO,MAAMmC,qBACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBL,yBACAlC,uBAAsB"}