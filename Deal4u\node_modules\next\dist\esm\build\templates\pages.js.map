{"version": 3, "sources": ["../../../src/build/templates/pages.ts"], "names": ["PagesRouteModule", "RouteKind", "hoist", "Document", "App", "userland", "getStaticProps", "getStaticPaths", "getServerSideProps", "config", "reportWebVitals", "unstable_getStaticProps", "unstable_getStaticPaths", "unstable_getStaticParams", "unstable_getServerProps", "unstable_getServerSideProps", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "bundlePath", "filename", "components"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,0DAAyD;AAC1F,SAASC,SAAS,QAAQ,iCAAgC;AAC1D,SAASC,KAAK,QAAQ,YAAW;AAEjC,uCAAuC;AACvC,OAAOC,cAAc,sBAAqB;AAC1C,OAAOC,SAAS,iBAAgB;AAEhC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;AAExC,0DAA0D;AAC1D,eAAeH,MAAMG,UAAU,WAAU;AAEzC,qBAAqB;AACrB,OAAO,MAAMC,iBAAiBJ,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAME,iBAAiBL,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAMG,qBAAqBN,MAAMG,UAAU,sBAAqB;AACvE,OAAO,MAAMI,SAASP,MAAMG,UAAU,UAAS;AAC/C,OAAO,MAAMK,kBAAkBR,MAAMG,UAAU,mBAAkB;AAEjE,4BAA4B;AAC5B,OAAO,MAAMM,0BAA0BT,MACrCG,UACA,2BACD;AACD,OAAO,MAAMO,0BAA0BV,MACrCG,UACA,2BACD;AACD,OAAO,MAAMQ,2BAA2BX,MACtCG,UACA,4BACD;AACD,OAAO,MAAMS,0BAA0BZ,MACrCG,UACA,2BACD;AACD,OAAO,MAAMU,8BAA8Bb,MACzCG,UACA,+BACD;AAED,4DAA4D;AAC5D,OAAO,MAAMW,cAAc,IAAIhB,iBAAiB;IAC9CiB,YAAY;QACVC,MAAMjB,UAAUkB,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,YAAY;QACVpB;QACAD;IACF;IACAE;AACF,GAAE"}