{"version": 3, "sources": ["../../src/lib/file-exists.ts"], "names": ["existsSync", "promises", "isError", "FileType", "File", "Directory", "fileExists", "fileName", "type", "stats", "stat", "isFile", "isDirectory", "err", "code"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,KAAI;AACzC,OAAOC,aAAa,aAAY;WAEzB;UAAKC,QAAQ;IAARA,SACVC,UAAO;IADGD,SAEVE,eAAY;GAFFF,aAAAA;AAKZ,OAAO,eAAeG,WACpBC,QAAgB,EAChBC,IAAe;IAEf,IAAI;QACF,IAAIA,SATC,QASuB;YAC1B,MAAMC,QAAQ,MAAMR,SAASS,IAAI,CAACH;YAClC,OAAOE,MAAME,MAAM;QACrB,OAAO,IAAIH,SAXD,aAW8B;YACtC,MAAMC,QAAQ,MAAMR,SAASS,IAAI,CAACH;YAClC,OAAOE,MAAMG,WAAW;QAC1B;QAEA,OAAOZ,WAAWO;IACpB,EAAE,OAAOM,KAAK;QACZ,IACEX,QAAQW,QACPA,CAAAA,IAAIC,IAAI,KAAK,YAAYD,IAAIC,IAAI,KAAK,cAAa,GACpD;YACA,OAAO;QACT;QACA,MAAMD;IACR;AACF"}