{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "names": ["React", "useContext", "use", "startTransition", "Suspense", "ReactDOM", "CacheStates", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "createInfinitePromise", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "handleSmoothScroll", "RedirectBoundary", "NotFoundBoundary", "getSegmentValue", "createRouterCache<PERSON>ey", "createRecordFromThenable", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "findDOMNode", "instance", "window", "process", "env", "NODE_ENV", "originalConsoleError", "console", "error", "messages", "includes", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "childProp", "tree", "cache<PERSON>ey", "buildId", "changeByServerResponse", "fullTree", "childNode", "get", "current", "status", "READY", "data", "subTreeData", "parallelRoutes", "Map", "set", "LAZY_INITIALIZED", "refetchTree", "DATA_FETCH", "URL", "location", "origin", "nextUrl", "head", "flightData", "overrideCanonicalUrl", "setTimeout", "subtree", "Provider", "value", "LoadingBoundary", "loading", "loadingStyles", "loadingScripts", "hasLoading", "fallback", "OuterLayoutRouter", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "notFoundStyles", "styles", "childNodesForParallelRouter", "treeSegment", "childPropSegment", "currentChildSegmentValue", "preservedSegments", "map", "preservedSegment", "isChildPropSegment", "preservedSegmentValue", "key", "errorComponent", "isActive"], "mappings": "AAAA;AAYA,OAAOA,SAASC,UAAU,EAAEC,GAAG,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,QAAO;AACzE,OAAOC,cAAc,YAAW;AAChC,SACEC,WAAW,EACXC,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAe,QACV,qDAAoD;AAC3D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,qDAAoD;AACvF,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,SAASC,wBAAwB,QAAQ,+CAA8C;AAEvF;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIb,aAAaS,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACK,cAAc,CAACH,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMG,UAAUR,eACdS,WACAP,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBI,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLN,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBS,KAAK,CAAC,IACxBR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,4FAA4F;AAC5F;;CAEC,GACD,SAASS,YACPC,QAAoD;IAEpD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,wDAAwD;IACxD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,uBAAuBC,QAAQC,KAAK;QAC1C,IAAI;YACFD,QAAQC,KAAK,GAAG;iDAAIC;oBAAAA;;gBAClB,4DAA4D;gBAC5D,IAAI,CAACA,QAAQ,CAAC,EAAE,CAACC,QAAQ,CAAC,6CAA6C;oBACrEJ,wBAAwBG;gBAC1B;YACF;YACA,OAAOnC,SAAS0B,WAAW,CAACC;QAC9B,SAAU;YACRM,QAAQC,KAAK,GAAGF;QAClB;IACF;IACA,OAAOhC,SAAS0B,WAAW,CAACC;AAC9B;AAEA,MAAMU,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACH,QAAQ,CAACI,iBAAiBD,SAASE,QAAQ,GAAG;QACpE,IAAIZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CE,QAAQS,IAAI,CACV,4FACAH;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMI,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmC7D,MAAM8D,SAAS;IAoGtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;;aAhHAN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC9C,MAAM,KAAK,KAC1C,CAACyC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYrB,KAAK,CAAC,CAAC3B,SAASoD,QAC1B9D,aAAaU,SAASmD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMpB,eAAeW,kBAAkBX,YAAY;gBAEnD,IAAIA,cAAc;oBAChBoB,UAAUrB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACoB,SAAS;oBACZA,UAAU7C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE6C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMnC,kBAAkBiC,SAAU;oBACtE,uGAAuG;oBACvG,IAAIA,QAAQG,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAH,UAAUA,QAAQG,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EZ,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBX,YAAY,GAAG;gBACjCW,kBAAkBK,YAAY,GAAG,EAAE;gBAEnC1D,mBACE;oBACE,uEAAuE;oBACvE,IAAI0C,cAAc;wBACdoB,QAAwBI,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAcxB,SAASyB,eAAe;oBAC5C,MAAM7B,iBAAiB4B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAI/B,uBAAuBwB,SAAwBvB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H4B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAChC,uBAAuBwB,SAAwBvB,iBAAiB;wBAEjEuB,QAAwBI,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBnB,kBAAkBmB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEnB,kBAAkBmB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BV,QAAQW,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BjB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMmB,UAAUxF,WAAWO;IAC3B,IAAI,CAACiF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,qBACE,oBAAC7B;QACCU,aAAaA;QACbJ,mBAAmBsB,QAAQtB,iBAAiB;OAE3CG;AAGP;AAEA;;CAEC,GACD,SAASqB,kBAAkB,KAmB1B;IAnB0B,IAAA,EACzBC,iBAAiB,EACjBC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTxB,WAAW,EACXyB,IAAI,EACJ,oDAAoD;IACpD,YAAY;IACZC,QAAQ,EAUT,GAnB0B;IAoBzB,MAAMR,UAAUxF,WAAWO;IAC3B,IAAI,CAACiF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAM,EAAEQ,OAAO,EAAEC,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGX;IAE5D,yDAAyD;IACzD,IAAIY,YAAYP,WAAWQ,GAAG,CAACL;IAE/B,mEAAmE;IACnE,IACEF,aACA,0DAA0D;IAC1DA,UAAUQ,OAAO,KAAK,MACtB;QACA,IAAI,CAACF,WAAW;YACd,8CAA8C;YAC9C,yJAAyJ;YACzJA,YAAY;gBACVG,QAAQlG,YAAYmG,KAAK;gBACzBC,MAAM;gBACNC,aAAaZ,UAAUQ,OAAO;gBAC9BK,gBAAgB,IAAIC;YACtB;YAEAf,WAAWgB,GAAG,CAACb,UAAUI;QAC3B,OAAO;YACL,IAAIA,UAAUG,MAAM,KAAKlG,YAAYyG,gBAAgB,EAAE;gBACrD,6CAA6C;gBAC7CV,UAAUG,MAAM,GAAGlG,YAAYmG,KAAK;gBACpC,mBAAmB;gBACnBJ,UAAUM,WAAW,GAAGZ,UAAUQ,OAAO;YAC3C;QACF;IACF;IAEA,oGAAoG;IACpG,IAAI,CAACF,aAAaA,UAAUG,MAAM,KAAKlG,YAAYyG,gBAAgB,EAAE;QACnE;;KAEC,GACD,sBAAsB;QACtB,MAAMC,cAAc5F,eAAe;YAAC;eAAOmD;SAAY,EAAE6B;QAEzDC,YAAY;YACVG,QAAQlG,YAAY2G,UAAU;YAC9BP,MAAMvF,yBACJT,oBACE,IAAIwG,IAAIrB,KAAKsB,SAASC,MAAM,GAC5BJ,aACAvB,QAAQ4B,OAAO,EACfnB;YAGJS,aAAa;YACbW,MACEjB,aAAaA,UAAUG,MAAM,KAAKlG,YAAYyG,gBAAgB,GAC1DV,UAAUiB,IAAI,GACdzF;YACN+E,gBACEP,aAAaA,UAAUG,MAAM,KAAKlG,YAAYyG,gBAAgB,GAC1DV,UAAUO,cAAc,GACxB,IAAIC;QACZ;QAEA;;KAEC,GACDf,WAAWgB,GAAG,CAACb,UAAUI;IAC3B;IAEA,kGAAkG;IAClG,IAAI,CAACA,WAAW;QACd,MAAM,IAAIX,MAAM;IAClB;IAEA,kGAAkG;IAClG,IAAIW,UAAUM,WAAW,IAAIN,UAAUK,IAAI,EAAE;QAC3C,MAAM,IAAIhB,MAAM;IAClB;IAEA,6FAA6F;IAC7F,IAAIW,UAAUK,IAAI,EAAE;QAClB;;KAEC,GACD,8DAA8D;QAC9D,MAAM,CAACa,YAAYC,qBAAqB,GAAGtH,IAAImG,UAAUK,IAAI;QAE7D,sEAAsE;QACtEL,UAAUK,IAAI,GAAG;QAEjB,wGAAwG;QACxGe,WAAW;YACTtH,gBAAgB;gBACdgG,uBAAuBC,UAAUmB,YAAYC;YAC/C;QACF;QACA,yGAAyG;QACzGtH,IAAIS;IACN;IAEA,yIAAyI;IACzI,wFAAwF;IACxF,IAAI,CAAC0F,UAAUM,WAAW,EAAE;QAC1BzG,IAAIS;IACN;IAEA,MAAM+G,UACJ,4EAA4E;kBAC5E,oBAACnH,oBAAoBoH,QAAQ;QAC3BC,OAAO;YACL5B,MAAMA,IAAI,CAAC,EAAE,CAACJ,kBAAkB;YAChCE,YAAYO,UAAUO,cAAc;YACpC,kDAAkD;YAClDf,KAAKA;QACP;OAECQ,UAAUM,WAAW;IAG1B,iFAAiF;IACjF,OAAOe;AACT;AAEA;;;CAGC,GACD,SAASG,gBAAgB,KAYxB;IAZwB,IAAA,EACvBvD,QAAQ,EACRwD,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EAOX,GAZwB;IAavB,IAAIA,YAAY;QACd,qBACE,oBAAC7H;YACC8H,wBACE,0CACGH,eACAC,gBACAF;WAIJxD;IAGP;IAEA,qBAAO,0CAAGA;AACZ;AAEA;;;CAGC,GACD,eAAe,SAAS6D,kBAAkB,KAkCzC;IAlCyC,IAAA,EACxCvC,iBAAiB,EACjBrB,WAAW,EACXwB,SAAS,EACTxD,KAAK,EACL6F,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfT,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVO,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACdC,MAAM,EAkBP,GAlCyC;IAmCxC,MAAMlD,UAAUxF,WAAWM;IAC3B,IAAI,CAACkF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAM,EAAEI,UAAU,EAAEE,IAAI,EAAEH,GAAG,EAAE,GAAGJ;IAElC,4CAA4C;IAC5C,IAAImD,8BAA8B9C,WAAWQ,GAAG,CAACV;IACjD,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACgD,6BAA6B;QAChCA,8BAA8B,IAAI/B;QAClCf,WAAWgB,GAAG,CAAClB,mBAAmBgD;IACpC;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMC,cAAc7C,IAAI,CAAC,EAAE,CAACJ,kBAAkB,CAAC,EAAE;IAEjD,MAAMkD,mBAAmB/C,UAAUxE,OAAO;IAE1C,gIAAgI;IAChI,MAAMwH,2BAA2B9H,gBAAgB4H;IAEjD;;GAEC,GACD,+DAA+D;IAC/D,MAAMG,oBAA+B;QAACH;KAAY;IAElD,qBACE,0CACGF,QACAK,kBAAkBC,GAAG,CAAC,CAACC;QACtB,MAAMC,qBAAqBtI,aACzBqI,kBACAJ;QAEF,MAAMM,wBAAwBnI,gBAAgBiI;QAC9C,MAAMjD,WAAW/E,qBAAqBgI;QAEtC,OACE;;;;;;;;UAQA,iBACA,oBAACzI,gBAAgBkH,QAAQ;YACvB0B,KAAKnI,qBAAqBgI,kBAAkB;YAC5CtB,qBACE,oBAACpC;gBAAsBjB,aAAaA;6BAClC,oBAAC3D;gBACC0I,gBAAgB/G;gBAChB6F,aAAaA;gBACbC,cAAcA;6BAEd,oBAACR;gBACCI,YAAYA;gBACZH,SAASA;gBACTC,eAAeA;gBACfC,gBAAgBA;6BAEhB,oBAAChH;gBACCyH,UAAUA;gBACVC,gBAAgBA;6BAEhB,oBAAC3H,sCACC,oBAAC4E;gBACCC,mBAAmBA;gBACnBC,KAAKA;gBACLG,MAAMA;gBACNF,YAAY8C;gBACZ7C,WAAWoD,qBAAqBpD,YAAY;gBAC5CxB,aAAaA;gBACb0B,UAAUA;gBACVsD,UACER,6BAA6BK;;WAU5Cd,gBACAC,iBACAC;IAGP;AAGN"}