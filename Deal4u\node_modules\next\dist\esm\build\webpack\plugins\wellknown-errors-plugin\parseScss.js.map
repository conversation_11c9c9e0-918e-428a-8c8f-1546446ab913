{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseScss.ts"], "names": ["bold", "cyan", "red", "yellow", "SimpleWebpackError", "regexScssError", "getScssError", "fileName", "fileContent", "err", "name", "res", "exec", "message", "reason", "_lineNumer", "<PERSON><PERSON><PERSON><PERSON>", "columnString", "lineNumber", "Math", "max", "parseInt", "column", "length", "frame", "codeFrameColumns", "require", "start", "line", "forceColor", "toString", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AACpE,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,MAAMC,iBACJ;AAEF,OAAO,SAASC,aACdC,QAAgB,EAChBC,WAA0B,EAC1BC,GAAU;IAEV,IAAIA,IAAIC,IAAI,KAAK,aAAa;QAC5B,OAAO;IACT;IAEA,MAAMC,MAAMN,eAAeO,IAAI,CAACH,IAAII,OAAO;IAC3C,IAAIF,KAAK;QACP,MAAM,GAAGG,QAAQC,YAAYC,aAAaC,aAAa,GAAGN;QAC1D,MAAMO,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,YAAY;QACpD,MAAMO,SAASL,CAAAA,gCAAAA,aAAcM,MAAM,KAAI;QAEvC,IAAIC;QACJ,IAAIhB,aAAa;YACf,IAAI;gBACF,MAAM,EACJiB,gBAAgB,EACjB,GAAGC,QAAQ;gBACZF,QAAQC,iBACNjB,aACA;oBAAEmB,OAAO;wBAAEC,MAAMV;wBAAYI;oBAAO;gBAAE,GACtC;oBAAEO,YAAY;gBAAK;YAEvB,EAAE,OAAM,CAAC;QACX;QAEA,OAAO,IAAIzB,mBACT,CAAC,EAAEH,KAAKM,UAAU,CAAC,EAAEJ,OAAOe,WAAWY,QAAQ,IAAI,CAAC,EAAE3B,OACpDmB,OAAOQ,QAAQ,IACf,CAAC,EACH5B,IAAIF,KAAK,iBAAiB+B,MAAM,CAAC,CAAC,EAAE,EAAEjB,OAAO,IAAI,EAAEU,SAASR,YAAY,CAAC;IAE7E;IAEA,OAAO;AACT"}