{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.tsx"], "names": ["React", "CloseIcon", "LeftRightDialogHeader", "children", "className", "previous", "next", "close", "buttonLeft", "useRef", "buttonRight", "buttonClose", "nav", "set<PERSON><PERSON>", "useState", "onNav", "useCallback", "el", "useEffect", "root", "getRootNode", "d", "self", "document", "handler", "e", "key", "stopPropagation", "current", "focus", "ShadowRoot", "a", "activeElement", "HTMLElement", "blur", "addEventListener", "removeEventListener", "div", "data-nextjs-dialog-left-right", "ref", "button", "type", "disabled", "undefined", "aria-disabled", "onClick", "svg", "viewBox", "fill", "xmlns", "title", "path", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "data-nextjs-errors-dialog-left-right-close-button", "aria-label", "span", "aria-hidden"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,SAAS,QAAQ,wBAAuB;AAUjD,MAAMC,wBACJ,SAASA,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACN,GAN8B;IAO7B,MAAMC,aAAaR,MAAMS,MAAM,CAA2B;IAC1D,MAAMC,cAAcV,MAAMS,MAAM,CAA2B;IAC3D,MAAME,cAAcX,MAAMS,MAAM,CAA2B;IAE3D,MAAM,CAACG,KAAKC,OAAO,GAAGb,MAAMc,QAAQ,CAAqB;IACzD,MAAMC,QAAQf,MAAMgB,WAAW,CAAC,CAACC;QAC/BJ,OAAOI;IACT,GAAG,EAAE;IAELjB,MAAMkB,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,MAAMC,IAAIC,KAAKC,QAAQ;QAEvB,SAASC,QAAQC,CAAgB;YAC/B,IAAIA,EAAEC,GAAG,KAAK,aAAa;gBACzBD,EAAEE,eAAe;gBACjB,IAAInB,WAAWoB,OAAO,EAAE;oBACtBpB,WAAWoB,OAAO,CAACC,KAAK;gBAC1B;gBACAxB,YAAYA;YACd,OAAO,IAAIoB,EAAEC,GAAG,KAAK,cAAc;gBACjCD,EAAEE,eAAe;gBACjB,IAAIjB,YAAYkB,OAAO,EAAE;oBACvBlB,YAAYkB,OAAO,CAACC,KAAK;gBAC3B;gBACAvB,QAAQA;YACV,OAAO,IAAImB,EAAEC,GAAG,KAAK,UAAU;gBAC7BD,EAAEE,eAAe;gBACjB,IAAIR,gBAAgBW,YAAY;oBAC9B,MAAMC,IAAIZ,KAAKa,aAAa;oBAC5B,IAAID,KAAKA,MAAMpB,YAAYiB,OAAO,IAAIG,aAAaE,aAAa;wBAC9DF,EAAEG,IAAI;wBACN;oBACF;gBACF;gBAEA,IAAI3B,OAAO;oBACTA;gBACF;YACF;QACF;QAEAY,KAAKgB,gBAAgB,CAAC,WAAWX;QACjC,IAAIL,SAASE,GAAG;YACdA,EAAEc,gBAAgB,CAAC,WAAWX;QAChC;QACA,OAAO;YACLL,KAAKiB,mBAAmB,CAAC,WAAWZ;YACpC,IAAIL,SAASE,GAAG;gBACdA,EAAEe,mBAAmB,CAAC,WAAWZ;YACnC;QACF;IACF,GAAG;QAACjB;QAAOK;QAAKN;QAAMD;KAAS;IAE/B,2EAA2E;IAC3E,2CAA2C;IAC3CL,MAAMkB,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,8CAA8C;QAC9C,IAAID,gBAAgBW,YAAY;YAC9B,MAAMC,IAAIZ,KAAKa,aAAa;YAE5B,IAAI3B,YAAY,MAAM;gBACpB,IAAIG,WAAWoB,OAAO,IAAIG,MAAMvB,WAAWoB,OAAO,EAAE;oBAClDpB,WAAWoB,OAAO,CAACM,IAAI;gBACzB;YACF,OAAO,IAAI5B,QAAQ,MAAM;gBACvB,IAAII,YAAYkB,OAAO,IAAIG,MAAMrB,YAAYkB,OAAO,EAAE;oBACpDlB,YAAYkB,OAAO,CAACM,IAAI;gBAC1B;YACF;QACF;IACF,GAAG;QAACtB;QAAKN;QAAMD;KAAS;IAExB,qBACE,oBAACgC;QAAIC,iCAAAA;QAA8BlC,WAAWA;qBAC5C,oBAACQ;QAAI2B,KAAKxB;qBACR,oBAACyB;QACCD,KAAK/B;QACLiC,MAAK;QACLC,UAAUrC,YAAY,OAAO,OAAOsC;QACpCC,iBAAevC,YAAY,OAAO,OAAOsC;QACzCE,SAASxC,mBAAAA,WAAYsC;qBAErB,oBAACG;QACCC,SAAQ;QACRC,MAAK;QACLC,OAAM;qBAEN,oBAACC,eAAM,2BACP,oBAACC;QACC9B,GAAE;QACF+B,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;wBAIrB,oBAACf;QACCD,KAAK7B;QACL+B,MAAK;QACLC,UAAUpC,QAAQ,OAAO,OAAOqC;QAChCC,iBAAetC,QAAQ,OAAO,OAAOqC;QACrCE,SAASvC,eAAAA,OAAQqC;qBAEjB,oBAACG;QACCC,SAAQ;QACRC,MAAK;QACLC,OAAM;qBAEN,oBAACC,eAAM,uBACP,oBAACC;QACC9B,GAAE;QACF+B,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;UAGZ,QAERpD,WAEFI,sBACC,oBAACiC;QACCgB,qDAAAA;QACAjB,KAAK5B;QACL8B,MAAK;QACLI,SAAStC;QACTkD,cAAW;qBAEX,oBAACC;QAAKC,eAAY;qBAChB,oBAAC1D,qBAGH;AAGV;AAEF,SAASC,qBAAqB,GAAE"}