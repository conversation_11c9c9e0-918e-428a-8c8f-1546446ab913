{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["React", "AppleWebAppMeta", "FormatDetectionMeta", "ItunesMeta", "BasicMeta", "ViewportMeta", "VerificationMeta", "AlternatesMetadata", "OpenGraphMetadata", "TwitterMetadata", "AppLinksMeta", "IconsMetadata", "resolveMetadata", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createDefaultMetadata", "createDefaultViewport", "isNotFoundError", "createMetadataComponents", "tree", "pathname", "searchParams", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "metadataContext", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "defaultViewport", "metadata", "viewport", "error", "errorMetadataItem", "errorConvention", "undefined", "resolvedError", "resolvedMetadata", "resolvedViewport", "parentParams", "metadataItems", "notFoundMetadataError", "notFoundMetadata", "notFoundViewport", "elements", "alternates", "itunes", "formatDetection", "verification", "appleWebApp", "openGraph", "twitter", "appLinks", "icons", "push", "meta", "name", "map", "el", "index", "cloneElement", "key", "MetadataOutlet"], "mappings": "AAGA,OAAOA,WAAW,QAAO;AACzB,SACEC,eAAe,EACfC,mBAAmB,EACnBC,UAAU,EACVC,SAAS,EACTC,YAAY,EACZC,gBAAgB,QACX,mBAAkB;AACzB,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,uBAAsB;AAC7B,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,UAAU,QAAQ,kBAAiB;AAK5C,SACEC,qBAAqB,EACrBC,qBAAqB,QAChB,qBAAoB;AAC3B,SAASC,eAAe,QAAQ,oCAAmC;AAEnE,+DAA+D;AAC/D,+DAA+D;AAC/D,sGAAsG;AACtG,0GAA0G;AAC1G,uEAAuE;AACvE,4EAA4E;AAC5E,OAAO,SAASC,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EAQV;IACC,MAAMC,kBAAkB;QACtBL;IACF;IAEA,IAAIM;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBhB;QACxB,MAAMiB,kBAAkBhB;QACxB,IAAIiB,WAAyCF;QAC7C,IAAIG,WAAyCF;QAC7C,IAAIG;QACJ,MAAMC,oBAAwC;YAAC;YAAM;YAAM;SAAK;QAChE,MAAMC,kBAAkBb,cAAc,aAAac,YAAYd;QAE/D,MAAM,CAACe,eAAeC,kBAAkBC,iBAAiB,GACvD,MAAM5B,gBAAgB;YACpBM;YACAuB,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBP;YACAf;YACAC;YACAe;YACAZ;QACF;QACF,IAAI,CAACc,eAAe;YAClBL,WAAWO;YACXR,WAAWO;YACXd,QAAQY;QACV,OAAO;YACLH,QAAQI;YACR,0FAA0F;YAC1F,kGAAkG;YAClG,kDAAkD;YAClD,IAAI,CAACf,aAAaP,gBAAgBsB,gBAAgB;gBAChD,MAAM,CAACK,uBAAuBC,kBAAkBC,iBAAiB,GAC/D,MAAMjC,gBAAgB;oBACpBM;oBACAuB,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBP;oBACAf;oBACAC;oBACAe,iBAAiB;oBACjBZ;gBACF;gBACFS,WAAWY;gBACXb,WAAWY;gBACXV,QAAQS,yBAAyBT;YACnC;YACAT,QAAQS;QACV;QAEA,MAAMY,WAAWjC,WAAW;YAC1BR,aAAa;gBAAE4B,UAAUA;YAAS;YAClC7B,UAAU;gBAAE4B;YAAS;YACrBzB,mBAAmB;gBAAEwC,YAAYf,SAASe,UAAU;YAAC;YACrD5C,WAAW;gBAAE6C,QAAQhB,SAASgB,MAAM;YAAC;YACrC9C,oBAAoB;gBAAE+C,iBAAiBjB,SAASiB,eAAe;YAAC;YAChE3C,iBAAiB;gBAAE4C,cAAclB,SAASkB,YAAY;YAAC;YACvDjD,gBAAgB;gBAAEkD,aAAanB,SAASmB,WAAW;YAAC;YACpD3C,kBAAkB;gBAAE4C,WAAWpB,SAASoB,SAAS;YAAC;YAClD3C,gBAAgB;gBAAE4C,SAASrB,SAASqB,OAAO;YAAC;YAC5C3C,aAAa;gBAAE4C,UAAUtB,SAASsB,QAAQ;YAAC;YAC3C3C,cAAc;gBAAE4C,OAAOvB,SAASuB,KAAK;YAAC;SACvC;QAED,IAAIjC,wBAAwBwB,SAASU,IAAI,eAAC,oBAACC;YAAKC,MAAK;;QAErD,qBACE,0CACGZ,SAASa,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAO7D,MAAM8D,YAAY,CAACF,IAA0B;gBAAEG,KAAKF;YAAM;QACnE;IAGN;IAEA,eAAeG;QACb,MAAM9B,QAAQ,MAAMR;QACpB,IAAIQ,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,OAAO;QAACL;QAAcmC;KAAe;AACvC"}