{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/next-font.ts"], "names": ["getClientStyleLoader", "cssFileResolve", "getNextFontLoader", "ctx", "postcss", "fontLoaderPath", "loaders", "isClient", "push", "hasAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "esModule", "url", "resourcePath", "experimental", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "mode", "getLocalIdent", "_context", "_localIdentName", "exportName", "_options", "meta", "fontFamilyHash", "fontLoader", "isDev"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,cAAc,QAAQ,iBAAgB;AAE/C,OAAO,SAASC,kBACdC,GAAyB,EACzBC,OAAY,EACZC,cAAsB;IAEtB,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVR,qBAAqB;YACnBS,WAAWN,IAAIM,SAAS;YACxBC,eAAeP,IAAIO,aAAa;YAChCC,aAAaR,IAAIQ,WAAW;QAC9B;IAEJ;IAEAL,QAAQE,IAAI,CAAC;QACXI,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPX;YACAY,eAAe;YACf,4CAA4C;YAC5CC,UAAU;YACVC,KAAK,CAACA,KAAaC,eACjBlB,eAAeiB,KAAKC,cAAchB,IAAIiB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BlB,eAAeiB,KAAKC,cAAchB,IAAIiB,YAAY,CAACC,UAAU;YAC/DG,SAAS;gBACP,mEAAmE;gBACnEC,wBAAwB;gBACxB,2CAA2C;gBAC3CC,kBAAkBvB,IAAIwB,QAAQ;gBAC9B,6DAA6D;gBAC7D,iCAAiC;gBACjCC,MAAM;gBACNC,eAAe,CACbC,UACAC,iBACAC,YACAC,UACAC;oBAEA,6BAA6B;oBAC7B,OAAO,CAAC,EAAE,EAAEF,WAAW,CAAC,EAAEE,KAAKC,cAAc,CAAC,CAAC;gBACjD;YACF;YACAC,YAAY;QACd;IACF;IAEA9B,QAAQE,IAAI,CAAC;QACXI,QAAQ;QACRG,SAAS;YACPsB,OAAOlC,IAAIO,aAAa;YACxBiB,UAAUxB,IAAIwB,QAAQ;YACtBhB,aAAaR,IAAIQ,WAAW;YAC5BN;YACAD;QACF;IACF;IAEA,OAAOE;AACT"}