{"version": 3, "sources": ["../../src/lib/is-error.ts"], "names": ["isPlainObject", "isError", "err", "getProperError", "process", "env", "NODE_ENV", "Error", "JSON", "stringify"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gCAA+B;AAU7D,eAAe,SAASC,QAAQC,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEA,OAAO,SAASC,eAAeD,GAAY;IACzC,IAAID,QAAQC,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOJ,QAAQ,aAAa;YAC9B,OAAO,IAAIK,MACT,oCACE;QAEN;QAEA,IAAIL,QAAQ,MAAM;YAChB,OAAO,IAAIK,MACT,8BACE;QAEN;IACF;IAEA,OAAO,IAAIA,MAAMP,cAAcE,OAAOM,KAAKC,SAAS,CAACP,OAAOA,MAAM;AACpE"}