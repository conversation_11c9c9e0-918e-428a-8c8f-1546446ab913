{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts"], "names": ["createRouterCache<PERSON>ey", "findHeadInCache", "cache", "parallelRoutes", "isLastItem", "Object", "keys", "length", "head", "key", "segment", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "childSegmentMap", "get", "cache<PERSON>ey", "cacheNode", "item", "undefined"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,6BAA4B;AAEjE,OAAO,SAASC,gBACdC,KAAgB,EAChBC,cAAoC;IAEpC,MAAMC,aAAaC,OAAOC,IAAI,CAACH,gBAAgBI,MAAM,KAAK;IAC1D,IAAIH,YAAY;QACd,OAAOF,MAAMM,IAAI;IACnB;IACA,IAAK,MAAMC,OAAON,eAAgB;QAChC,MAAM,CAACO,SAASC,oBAAoB,GAAGR,cAAc,CAACM,IAAI;QAC1D,MAAMG,kBAAkBV,MAAMC,cAAc,CAACU,GAAG,CAACJ;QACjD,IAAI,CAACG,iBAAiB;YACpB;QACF;QAEA,MAAME,WAAWd,qBAAqBU;QAEtC,MAAMK,YAAYH,gBAAgBC,GAAG,CAACC;QACtC,IAAI,CAACC,WAAW;YACd;QACF;QAEA,MAAMC,OAAOf,gBAAgBc,WAAWJ;QACxC,IAAIK,MAAM;YACR,OAAOA;QACT;IACF;IAEA,OAAOC;AACT"}