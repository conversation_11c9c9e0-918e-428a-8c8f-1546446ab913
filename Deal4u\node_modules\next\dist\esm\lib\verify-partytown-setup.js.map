{"version": 3, "sources": ["../../src/lib/verify-partytown-setup.ts"], "names": ["promises", "bold", "cyan", "red", "path", "hasNecessaryDependencies", "fileExists", "FileType", "FatalE<PERSON>r", "Log", "getPkgManager", "missingDependencyError", "dir", "packageManager", "copyPartytownStaticFiles", "deps", "staticDir", "partytownLibDir", "join", "hasPartytownLibDir", "Directory", "rm", "recursive", "force", "copyLibFiles", "Promise", "resolve", "require", "resolved", "get", "verifyPartytownSetup", "targetDir", "partytownDeps", "file", "pkg", "exportsRestrict", "missing", "length", "err", "warn", "console", "error", "message", "process", "exit"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,EAAEC,IAAI,EAAEC,GAAG,QAAQ,eAAc;AAE9C,OAAOC,UAAU,OAAM;AACvB,SAASC,wBAAwB,QAAQ,+BAA8B;AAEvE,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAe;AACpD,SAASC,UAAU,QAAQ,gBAAe;AAC1C,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,aAAa,QAAQ,4BAA2B;AAEzD,eAAeC,uBAAuBC,GAAW;IAC/C,MAAMC,iBAAiBH,cAAcE;IAErC,MAAM,IAAIJ,WACRP,KACEE,IACE,uHAGF,SACAF,KAAK,CAAC,oCAAoC,CAAC,IAC3C,SACA,CAAC,EAAE,EAAEA,KACHC,KACE,AAACW,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IAAK,2BAElC,CAAC,GACH,SACAZ,KACE,CAAC,wEAAwE,EAAEC,KACzE,uBACA,wBAAwB,CAAC,IAE7B;AAEN;AAEA,eAAeY,yBACbC,IAA2B,EAC3BC,SAAiB;IAEjB,MAAMC,kBAAkBb,KAAKc,IAAI,CAACF,WAAW;IAC7C,MAAMG,qBAAqB,MAAMb,WAC/BW,iBACAV,SAASa,SAAS;IAGpB,IAAID,oBAAoB;QACtB,MAAMnB,SAASqB,EAAE,CAACJ,iBAAiB;YAAEK,WAAW;YAAMC,OAAO;QAAK;IACpE;IAEA,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAMC,QAAQC,OAAO,CAC5CC,QAAQvB,KAAKc,IAAI,CAACH,KAAKa,QAAQ,CAACC,GAAG,CAAC,0BAA2B;IAGjE,MAAML,aAAaP;AACrB;AAEA,OAAO,eAAea,qBACpBlB,GAAW,EACXmB,SAAiB;IAEjB,IAAI;YAYEC;QAXJ,MAAMA,gBAAuC,MAAM3B,yBACjDO,KACA;YACE;gBACEqB,MAAM;gBACNC,KAAK;gBACLC,iBAAiB;YACnB;SACD;QAGH,IAAIH,EAAAA,yBAAAA,cAAcI,OAAO,qBAArBJ,uBAAuBK,MAAM,IAAG,GAAG;YACrC,MAAM1B,uBAAuBC;QAC/B,OAAO;YACL,IAAI;gBACF,MAAME,yBAAyBkB,eAAeD;YAChD,EAAE,OAAOO,KAAK;gBACZ7B,IAAI8B,IAAI,CACN,CAAC,wFAAwF,EAAEtC,KACzFC,KAAK,0BACL,8BAA8B,CAAC;YAErC;QACF;IACF,EAAE,OAAOoC,KAAK;QACZ,8EAA8E;QAC9E,IAAIA,eAAe9B,YAAY;YAC7BgC,QAAQC,KAAK,CAACH,IAAII,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMN;IACR;AACF"}