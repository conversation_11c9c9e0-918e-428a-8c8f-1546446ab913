{"version": 3, "sources": ["../../../src/lib/typescript/diagnosticFormatter.ts"], "names": ["bold", "cyan", "red", "yellow", "path", "DiagnosticCategory", "Warning", "Error", "Suggestion", "Message", "getFormattedLinkDiagnosticMessageText", "diagnostic", "message", "messageText", "code", "match", "href", "relatedMessage", "relatedInformation", "suggestion", "getFormattedLayoutAndPageDiagnosticMessageText", "relativeSourceFilepath", "type", "test", "filepathAndType", "main", "processNext", "indent", "next", "item", "mismatchedField", "repeat", "types", "replace", "invalidConfig", "<PERSON><PERSON><PERSON>", "invalidProp", "invalid", "incompatPageProp", "extraLayoutProp", "invalidExportFnArg", "processNextItems", "result", "invalidParamFn", "invalidExportFnReturn", "filepathAndInvalidExport", "getAppEntrySourceFilePath", "baseDir", "sourceFilepath", "file", "text", "trim", "relative", "getFormattedDiagnostic", "ts", "distDir", "isAppDirEnabled", "isLayoutOrPageError", "fileName", "startsWith", "join", "appPath", "linkReason", "appReason", "reason", "flattenDiagnosticMessageText", "category", "codeFrameColumns", "require", "pos", "getLineAndCharacterOfPosition", "start", "line", "character", "posix", "normalize", "toString", "getFullText", "getSourceFile", "column", "forceColor"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,gBAAe;AACvD,OAAOC,UAAU,OAAM;WAIhB;UAAKC,kBAAkB;IAAlBA,mBAAAA,mBACVC,aAAU,KAAVA;IADUD,mBAAAA,mBAEVE,WAAQ,KAARA;IAFUF,mBAAAA,mBAGVG,gBAAa,KAAbA;IAHUH,mBAAAA,mBAIVI,aAAU,KAAVA;GAJUJ,uBAAAA;AAOZ,SAASK,sCACPC,UAA2C;IAE3C,MAAMC,UAAUD,WAAWE,WAAW;IACtC,IAAI,OAAOD,YAAY,YAAYD,WAAWG,IAAI,KAAK,MAAM;QAC3D,MAAMC,QACJH,QAAQG,KAAK,CACX,6EAEFH,QAAQG,KAAK,CACX;QAGJ,IAAIA,OAAO;YACT,MAAM,GAAGC,KAAK,GAAGD;YACjB,OAAO,CAAC,CAAC,EAAEf,KACTgB,MACA,8FAA8F,CAAC;QACnG,OAAO,IACLJ,YAAY,wDACZ;gBACuBD,iCAAAA;YAAvB,MAAMM,kBAAiBN,iCAAAA,WAAWO,kBAAkB,sBAA7BP,kCAAAA,8BAA+B,CAAC,EAAE,qBAAlCA,gCAAoCE,WAAW;YACtE,IACE,OAAOI,mBAAmB,YAC1BA,eAAeF,KAAK,CAClB,wGAEF;gBACA,OAAO,CAAC,mIAAmI,CAAC;YAC9I;QACF;IACF,OAAO,IAAI,OAAOH,YAAY,YAAYD,WAAWG,IAAI,KAAK,MAAM;QAClE,MAAMC,QACJH,QAAQG,KAAK,CACX,oGAEFH,QAAQG,KAAK,CACX;QAGJ,IAAIA,OAAO;YACT,MAAM,GAAGC,MAAMG,WAAW,GAAGJ;YAC7B,OAAO,CAAC,CAAC,EAAEf,KAAKgB,MAAM,0CAA0C,EAAEhB,KAChEmB,YACA,6EAA6E,CAAC;QAClF;IACF;AACF;AAEA,SAASC,+CACPC,sBAA8B,EAC9BV,UAA2C;IAE3C,MAAMC,UACJ,OAAOD,WAAWE,WAAW,KAAK,WAC9BF,aACAA,WAAWE,WAAW;IAC5B,MAAMA,cAAcD,QAAQC,WAAW;IAEvC,IAAI,OAAOA,gBAAgB,UAAU;QACnC,MAAMS,OAAO,eAAeC,IAAI,CAACF,0BAC7B,SACA,gBAAgBE,IAAI,CAACF,0BACrB,UACA;QAEJ,4BAA4B;QAC5B,yFAAyF;QACzF,OAAQT,QAAQE,IAAI;YAClB,KAAK;gBACH,MAAMU,kBAAkBX,YAAYE,KAAK,CAAC;gBAC1C,IAAIS,iBAAiB;oBACnB,IAAIC,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEtB,KACrBqB,wBACA,iDAAiD,EAAEC,KAAK,CAAC,CAAC;oBAE5D,SAASI,YACPC,MAAc,EACdC,IAAoD;wBAEpD,IAAI,CAACA,MAAM;wBAEX,KAAK,MAAMC,QAAQD,KAAM;4BACvB,OAAQC,KAAKf,IAAI;gCACf,KAAK;oCACH,MAAMgB,kBACJD,KAAKhB,WAAW,CAACE,KAAK,CAAC;oCACzB,IAAIe,iBAAiB;wCACnBL,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,CAAC,EAAEzB,KAAK8B,eAAe,CAAC,EAAE,EAAE,qBAAqB,CAAC;oCAC7D;oCACA;gCACF,KAAK;oCACH,MAAME,QAAQH,KAAKhB,WAAW,CAACE,KAAK,CAClC;oCAEF,IAAIiB,OAAO;wCACTP,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCAEnC,IACEK,KAAK,CAAC,EAAE,KAAK,mBACbA,KAAK,CAAC,EAAE,KAAK,mBACb;4CACAP,QAAQ,CAAC,aAAa,EAAEH,KAAK,iCAAiC,CAAC;wCACjE,OAAO;4CACLG,QAAQ,CAAC,UAAU,EAAEzB,KACnBgC,KAAK,CAAC,EAAE,CAACC,OAAO,CACd,iCACA,kBAEF,QAAQ,EAAEjC,KAAKgC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;wCAChC;oCACF;oCACA;gCACF,KAAK;oCACH,MAAME,gBAAgBL,KAAKhB,WAAW,CAACE,KAAK,CAC1C;oCAEFU,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;oCACnCF,QAAQ,CAAC,qBAAqB,EAC5BS,gBAAgB,CAAC,EAAE,EAAElC,KAAKkC,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAClD,CAAC,CAAC;oCACH;gCACF,KAAK;oCACH,MAAMC,eAAeN,KAAKhB,WAAW,CAACE,KAAK,CACzC;oCAEF,IAAIoB,cAAc;wCAChBV,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,CAAC,EAAEzB,KACVmC,YAAY,CAAC,EAAE,EACf,iBAAiB,EAAEb,KAAK,cAAc,CAAC;oCAC3C;oCACA;gCACF,KAAK;oCACH,MAAMc,cAAcP,KAAKhB,WAAW,CAACE,KAAK,CACxC;oCAEF,IAAIqB,aAAa;wCACf,IACEA,WAAW,CAAC,EAAE,KAAK,iBACnBA,WAAW,CAAC,EAAE,KAAK,aACnB;4CACAX,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;4CACnCF,QAAQ,CAAC,MAAM,EAAEW,WAAW,CAAC,EAAE,CAAC,2BAA2B,EAAEd,KAAK,CAAC,CAAC;wCACtE;oCACF;oCACA;gCACF,KAAK;oCACH,MAAMe,UAAUR,KAAKhB,WAAW,CAACE,KAAK,CAAC;oCACvC,IAAIsB,SAAS;wCACXZ,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,MAAM,EAAEzB,KAAKqC,OAAO,CAAC,EAAE,EAAE,gBAAgB,CAAC;oCACrD;oCACA;gCACF,KAAK;oCACH,MAAMC,mBAAmBT,KAAKhB,WAAW,CAACE,KAAK,CAC7C;oCAEF,IAAIuB,kBAAkB;wCACpBb,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;wCACnCF,QAAQ,CAAC,MAAM,EAAEzB,KACfsC,gBAAgB,CAAC,EAAE,EACnB,6DAA6D,CAAC;oCAClE,OAAO;wCACL,MAAMC,kBAAkBV,KAAKhB,WAAW,CAACE,KAAK,CAC5C;wCAEF,IAAIwB,iBAAiB;4CACnBd,QAAQ,OAAO,IAAIM,MAAM,CAACJ,SAAS;4CACnCF,QAAQ,CAAC,MAAM,EAAEzB,KACfuC,eAAe,CAAC,EAAE,EAClB,iDAAiD,CAAC;wCACtD;oCACF;oCACA;gCACF;4BACF;4BAEAb,YAAYC,SAAS,GAAGE,KAAKD,IAAI;wBACnC;oBACF;oBAEA,IAAI,UAAUhB,SAASc,YAAY,GAAGd,QAAQgB,IAAI;oBAClD,OAAOH;gBACT;gBAEA,MAAMe,qBAAqB3B,YAAYE,KAAK,CAC1C;gBAEF,IAAIyB,oBAAoB;oBACtB,MAAMf,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEtB,KACvBqB,wBACA,kBAAkB,EAAErB,KACpBwC,kBAAkB,CAAC,EAAE,EACrB,mBAAmB,EAAExC,KAAKwC,kBAAkB,CAAC,EAAE,EAAE,eAAe,CAAC;oBACnE,OAAOf;gBACT;gBAEA,SAASgB,iBACPd,MAAc,EACdC,IAAoD;oBAEpD,IAAI,CAACA,MAAM,OAAO;oBAElB,IAAIc,SAAS;oBAEb,KAAK,MAAMb,QAAQD,KAAM;wBACvB,OAAQC,KAAKf,IAAI;4BACf,KAAK;gCACH,MAAMkB,QAAQH,KAAKhB,WAAW,CAACE,KAAK,CAClC;gCAEF,IAAIiB,OAAO;oCACTU,UAAU,OAAO,IAAIX,MAAM,CAACJ,SAAS;oCACrCe,UAAU,CAAC,UAAU,EAAE1C,KAAKgC,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAEhC,KAC9CgC,KAAK,CAAC,EAAE,EACR,EAAE,CAAC;gCACP;gCACA;4BACF;wBACF;wBAEAU,UAAUD,iBAAiBd,SAAS,GAAGE,KAAKD,IAAI;oBAClD;oBAEA,OAAOc;gBACT;gBAEA,MAAMC,iBAAiB9B,YAAYE,KAAK,CACtC;gBAEF,IAAI4B,gBAAgB;oBAClB,IAAIlB,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEtB,KACrBqB,wBACA,iBAAiB,EAAEsB,cAAc,CAAC,EAAE,CAAC,kBAAkB,EAAE3C,KACzD2C,cAAc,CAAC,EAAE,EACjB,yCAAyC,EACzCA,cAAc,CAAC,EAAE,CAClB,UAAU,CAAC;oBAEZ,IAAI,UAAU/B,SAASa,QAAQgB,iBAAiB,GAAG7B,QAAQgB,IAAI;oBAC/D,OAAOH;gBACT;gBAEA,MAAMmB,wBAAwB/B,YAAYE,KAAK,CAC7C;gBAEF,IAAI6B,uBAAuB;oBACzB,IAAInB,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEtB,KACrBqB,wBACA,6BAA6B,EAAErB,KAC/B4C,qBAAqB,CAAC,EAAE,EACxB,iBAAiB,EAAEA,qBAAqB,CAAC,EAAE,CAAC,aAAa,CAAC;oBAE5D,IAAI,UAAUhC,SAASa,QAAQgB,iBAAiB,GAAG7B,QAAQgB,IAAI;oBAC/D,OAAOH;gBACT;gBAEA;YACF,KAAK;gBACH,MAAMoB,2BAA2BhC,YAAYE,KAAK,CAChD;gBAEF,IAAI8B,0BAA0B;oBAC5B,MAAMpB,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEtB,KACvBqB,wBACA,sBAAsB,EAAErB,KACxB6C,wBAAwB,CAAC,EAAE,EAC3B,SAAS,EAAEvB,KAAK,4IAA4I,CAAC;oBAC/J,OAAOG;gBACT;gBACA;YACF,KAAK;gBACH,MAAMY,UAAUxB,YAAYE,KAAK,CAC/B;gBAEF,IAAIsB,SAAS;oBACX,MAAMZ,OAAO,CAAC,EAAEH,KAAK,EAAE,EAAEtB,KACvBqB,wBACA,4BAA4B,EAAErB,KAAKqC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAEA,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrE,OAAOZ;gBACT;gBACA;YACF;QACF;IACF;AACF;AAEA,SAASqB,0BACPC,OAAe,EACfpC,UAA2C;QAGzCA,kCAAAA;IADF,MAAMqC,iBACJrC,EAAAA,mBAAAA,WAAWsC,IAAI,sBAAftC,mCAAAA,iBAAiBuC,IAAI,CAACC,IAAI,GAAGpC,KAAK,CAAC,0CAAnCJ,gCAA0D,CAAC,EAAE,KAAI;IAEnE,OAAOP,KAAKgD,QAAQ,CAACL,SAASC;AAChC;AAEA,OAAO,SAASK,uBACdC,EAA+B,EAC/BP,OAAe,EACfQ,OAAe,EACf5C,UAA2C,EAC3C6C,eAAyB;QAKvB7C;IAHF,gEAAgE;IAChE,MAAM8C,sBACJD,qBACA7C,mBAAAA,WAAWsC,IAAI,qBAAftC,iBAAiB+C,QAAQ,CAACC,UAAU,CAACvD,KAAKwD,IAAI,CAACb,SAASQ,SAAS;IAEnE,IAAI3C,UAAU;IAEd,MAAMiD,UAAUJ,sBACZX,0BAA0BC,SAASpC,cACnC;IACJ,MAAMmD,aAAapD,sCAAsCC;IACzD,MAAMoD,YACJ,CAACD,cAAcL,uBAAuBI,UAClCzC,+CAA+CyC,SAASlD,cACxD;IAEN,MAAMqD,SACJF,cACAC,aACAT,GAAGW,4BAA4B,CAACtD,WAAWE,WAAW,EAAE;IAC1D,MAAMqD,WAAWvD,WAAWuD,QAAQ;IACpC,OAAQA;QACN,UAAU;QACV,KA/UQ;YA+UyB;gBAC/BtD,WAAWT,OAAOH,KAAK,mBAAmB;gBAC1C;YACF;QACA,QAAQ;QACR,KAnVM;YAmVyB;gBAC7BY,WAAWV,IAAIF,KAAK,iBAAiB;gBACrC;YACF;QACA,8BAA8B;QAC9B,KAvVW;QAwVX,KAvVQ;QAwVR;YAAS;gBACPY,WAAWX,KAAKD,KAAKkE,aAAa,IAAI,eAAe,WAAW;gBAChE;YACF;IACF;IAEAtD,WAAWoD,SAAS;IAEpB,IAAI,CAACP,uBAAuB9C,WAAWsC,IAAI,EAAE;QAC3C,MAAM,EAAEkB,gBAAgB,EAAE,GAAGC,QAAQ;QACrC,MAAMC,MAAM1D,WAAWsC,IAAI,CAACqB,6BAA6B,CAAC3D,WAAW4D,KAAK;QAC1E,MAAMC,OAAOH,IAAIG,IAAI,GAAG;QACxB,MAAMC,YAAYJ,IAAII,SAAS,GAAG;QAElC,IAAIf,WAAWtD,KAAKsE,KAAK,CAACC,SAAS,CACjCvE,KAAKgD,QAAQ,CAACL,SAASpC,WAAWsC,IAAI,CAACS,QAAQ,EAAEzB,OAAO,CAAC,OAAO;QAElE,IAAI,CAACyB,SAASC,UAAU,CAAC,MAAM;YAC7BD,WAAW,OAAOA;QACpB;QAEA9C,UACEX,KAAKyD,YACL,MACAvD,OAAOqE,KAAKI,QAAQ,MACpB,MACAzE,OAAOsE,UAAUG,QAAQ,MACzB,OACAhE;QAEFA,WACE,OACAuD,iBACExD,WAAWsC,IAAI,CAAC4B,WAAW,CAAClE,WAAWsC,IAAI,CAAC6B,aAAa,KACzD;YACEP,OAAO;gBAAEC,MAAMA;gBAAMO,QAAQN;YAAU;QACzC,GACA;YAAEO,YAAY;QAAK;IAEzB,OAAO,IAAIvB,uBAAuBI,SAAS;QACzCjD,UAAUX,KAAK4D,WAAW,OAAOjD;IACnC;IAEA,OAAOA;AACT"}