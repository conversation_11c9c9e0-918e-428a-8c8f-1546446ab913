// Admin Panel Functionality
class AdminManager {
    constructor() {
        this.currentSection = 'dashboard';
        this.products = [];
        this.isAutoSyncEnabled = false;
        this.init();
    }

    init() {
        this.checkAdminAccess();
        this.bindEvents();
        this.loadDashboard();
        this.checkApiStatus();
    }

    checkAdminAccess() {
        // For demo purposes, bypass authentication check
        // In production, you should implement proper authentication

        // Auto-login as admin for demo
        const demoAdmin = {
            id: 1,
            email: '<EMAIL>',
            name: 'Admin',
            role: 'admin',
            loginAt: new Date().toISOString(),
            avatar: 'https://ui-avatars.com/api/?name=Admin&background=667eea&color=fff'
        };

        // Save demo admin user
        if (typeof setStorage === 'function') {
            setStorage('deal4u_user', demoAdmin);
        } else {
            localStorage.setItem('deal4u_user', JSON.stringify(demoAdmin));
        }

        return true;
    }

    bindEvents() {
        // Navigation
        $$('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('.nav-link').dataset.section;
                this.showSection(section);
            });
        });

        // Product filters
        const statusFilter = $('#product-status-filter');
        const categoryFilter = $('#product-category-filter');
        const searchInput = $('#product-search');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterProducts());
        }

        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.filterProducts());
        }

        if (searchInput) {
            const debouncedSearch = debounce(() => this.filterProducts(), 300);
            searchInput.addEventListener('input', debouncedSearch);
        }
    }

    showSection(sectionName) {
        // Hide all sections
        $$('.admin-section').forEach(section => {
            removeClass(section, 'active');
        });

        // Show selected section
        const targetSection = $(`#${sectionName}-section`);
        if (targetSection) {
            addClass(targetSection, 'active');
        }

        // Update navigation
        $$('.nav-link').forEach(link => {
            removeClass(link, 'active');
            if (link.dataset.section === sectionName) {
                addClass(link, 'active');
            }
        });

        this.currentSection = sectionName;

        // Load section-specific data
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'products':
                this.loadProducts();
                break;
            case 'sync':
                this.loadSyncStatus();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    async loadDashboard() {
        try {
            // For demo purposes, use mock data if API is not available
            let products = [];

            if (typeof wooAPI !== 'undefined' && wooAPI.getAllProducts) {
                try {
                    products = await wooAPI.getAllProducts(true);
                } catch (apiError) {
                    console.log('API not available, using demo data');
                    // Use demo data
                    products = this.getDemoProducts();
                }
            } else {
                // Use demo data
                products = this.getDemoProducts();
            }

            this.products = products;

            const totalCount = products.length;
            const publishedCount = products.filter(p => p.status === 'publish').length;
            const draftCount = products.filter(p => p.status === 'draft').length;
            const saleCount = products.filter(p => p.on_sale).length;

            // Update dashboard stats
            this.updateStat('total-products-count', totalCount);
            this.updateStat('published-products-count', publishedCount);
            this.updateStat('draft-products-count', draftCount);
            this.updateStat('sale-products-count', saleCount);

            // Update last sync time
            const lastSync = this.getStorage('last_sync_time');
            if (lastSync) {
                const lastSyncElement = document.getElementById('last-sync');
                if (lastSyncElement) {
                    lastSyncElement.textContent = this.timeAgo(lastSync);
                }
            }

        } catch (error) {
            console.error('Error loading dashboard:', error);
            this.showToast('Error loading dashboard data', 'error');
        }
    }

    getDemoProducts() {
        return [
            { id: 1, name: 'Demo Product 1', status: 'publish', on_sale: false, price: '29.99' },
            { id: 2, name: 'Demo Product 2', status: 'publish', on_sale: true, price: '19.99' },
            { id: 3, name: 'Demo Product 3', status: 'draft', on_sale: false, price: '39.99' },
            { id: 4, name: 'Demo Product 4', status: 'publish', on_sale: false, price: '49.99' }
        ];
    }

    getStorage(key, defaultValue = null) {
        try {
            if (typeof getStorage === 'function') {
                return getStorage(key, defaultValue);
            }
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            return defaultValue;
        }
    }

    showToast(message, type = 'info') {
        if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
            alert(message);
        }
    }

    timeAgo(dateString) {
        if (typeof timeAgo === 'function') {
            return timeAgo(dateString);
        }
        return new Date(dateString).toLocaleString();
    }

    updateStat(elementId, value) {
        const element = this.$(elementId);
        if (element) {
            // Animate number counting
            this.animateNumber(element, 0, value, 1000);
        }
    }

    $(selector) {
        if (selector.startsWith('#')) {
            return document.getElementById(selector.substring(1));
        }
        return document.querySelector(selector);
    }

    $$(selector) {
        return document.querySelectorAll(selector);
    }

    animateNumber(element, start, end, duration) {
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (end - start) * progress);
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        requestAnimationFrame(animate);
    }

    async checkApiStatus() {
        try {
            await wooAPI.makeRequest('/products?per_page=1');
            $('#api-status').textContent = 'Connected';
            $('#api-status').className = 'status-badge';
        } catch (error) {
            $('#api-status').textContent = 'Error';
            $('#api-status').className = 'status-badge error';
        }
    }

    async loadProducts() {
        const tableBody = $('#products-table-body');
        if (!tableBody) return;

        try {
            showLoading(tableBody, 'Loading products...');
            
            if (this.products.length === 0) {
                this.products = await wooAPI.getAllProducts(true);
            }

            this.renderProductsTable(this.products);
        } catch (error) {
            console.error('Error loading products:', error);
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="error-row">
                        <p>Error loading products. Please check your API configuration.</p>
                    </td>
                </tr>
            `;
        }
    }

    renderProductsTable(products) {
        const tableBody = $('#products-table-body');
        if (!tableBody) return;

        if (products.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="empty-row">
                        <p>No products found.</p>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = products.map(product => `
            <tr>
                <td>
                    <img src="${getImageUrl(product)}" alt="${product.name}" 
                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                </td>
                <td>
                    <div style="max-width: 200px;">
                        <strong>${truncate(product.name, 50)}</strong>
                        <br>
                        <small>ID: ${product.id}</small>
                    </div>
                </td>
                <td>
                    <strong>${formatPrice(product.price)}</strong>
                    ${product.regular_price && product.regular_price !== product.price ? 
                        `<br><small style="text-decoration: line-through;">${formatPrice(product.regular_price)}</small>` : ''}
                </td>
                <td>
                    <span class="status-badge ${product.status === 'publish' ? '' : 'warning'}">
                        ${capitalizeFirst(product.status)}
                    </span>
                </td>
                <td>
                    <span class="category-tag">
                        ${this.getProductCategory(product)}
                    </span>
                </td>
                <td>
                    ${product.stock_status === 'instock' ? 
                        `<span class="status-badge">In Stock</span>` : 
                        `<span class="status-badge error">Out of Stock</span>`}
                </td>
                <td>
                    <div class="table-actions">
                        <button class="table-btn" onclick="adminManager.viewProduct(${product.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="table-btn" onclick="adminManager.editProduct(${product.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    getProductCategory(product) {
        const category = getCategoryByKeywords(product.name, product.short_description);
        return CONFIG.CATEGORIES[category]?.name || 'Uncategorized';
    }

    filterProducts() {
        const statusFilter = $('#product-status-filter')?.value || 'all';
        const categoryFilter = $('#product-category-filter')?.value || 'all';
        const searchQuery = $('#product-search')?.value.toLowerCase() || '';

        let filtered = [...this.products];

        // Filter by status
        if (statusFilter !== 'all') {
            filtered = filtered.filter(product => product.status === statusFilter);
        }

        // Filter by category
        if (categoryFilter !== 'all') {
            filtered = filtered.filter(product => {
                const productCategory = getCategoryByKeywords(product.name, product.short_description);
                return productCategory === categoryFilter;
            });
        }

        // Filter by search query
        if (searchQuery) {
            filtered = filtered.filter(product =>
                product.name.toLowerCase().includes(searchQuery) ||
                product.id.toString().includes(searchQuery)
            );
        }

        this.renderProductsTable(filtered);
    }

    async syncAllProducts() {
        this.logSync('Starting full product sync...');
        
        try {
            const products = await wooAPI.getAllProducts(true);
            this.products = products;
            
            // Clear cache to force refresh
            wooAPI.clearCache();
            
            // Update dashboard
            this.loadDashboard();
            
            // Save sync time
            setStorage('last_sync_time', new Date().toISOString());
            
            this.logSync(`Successfully synced ${products.length} products`);
            showToast(`Synced ${products.length} products successfully!`, 'success');
            
        } catch (error) {
            console.error('Sync error:', error);
            this.logSync(`Sync failed: ${error.message}`);
            showToast('Sync failed. Please check your API configuration.', 'error');
        }
    }

    async fullSync() {
        await this.syncAllProducts();
    }

    async publishedSync() {
        this.logSync('Starting published products sync...');
        
        try {
            const products = await wooAPI.getProducts({ status: 'publish' });
            
            // Clear cache
            wooAPI.clearCache();
            
            this.logSync(`Successfully synced ${products.length} published products`);
            showToast(`Synced ${products.length} published products!`, 'success');
            
        } catch (error) {
            console.error('Published sync error:', error);
            this.logSync(`Published sync failed: ${error.message}`);
            showToast('Published sync failed.', 'error');
        }
    }

    toggleAutoSync() {
        this.isAutoSyncEnabled = !this.isAutoSyncEnabled;
        const btn = $('#auto-sync-btn');
        
        if (this.isAutoSyncEnabled) {
            btn.innerHTML = '<i class="fas fa-robot"></i> Disable Auto Sync';
            btn.style.background = '#ef4444';
            showToast('Auto sync enabled', 'success');
            this.logSync('Auto sync enabled');
        } else {
            btn.innerHTML = '<i class="fas fa-robot"></i> Enable Auto Sync';
            btn.style.background = '#667eea';
            showToast('Auto sync disabled', 'info');
            this.logSync('Auto sync disabled');
        }
        
        setStorage('auto_sync_enabled', this.isAutoSyncEnabled);
    }

    logSync(message) {
        const logContent = $('#sync-log-content');
        if (logContent) {
            const timestamp = new Date().toLocaleString();
            const logEntry = `<div class="log-entry">[${timestamp}] ${message}</div>`;
            
            if (logContent.innerHTML.includes('No sync operations')) {
                logContent.innerHTML = logEntry;
            } else {
                logContent.innerHTML = logEntry + logContent.innerHTML;
            }
        }
    }

    loadSyncStatus() {
        const autoSyncEnabled = getStorage('auto_sync_enabled', false);
        this.isAutoSyncEnabled = autoSyncEnabled;
        
        const btn = $('#auto-sync-btn');
        if (btn) {
            if (autoSyncEnabled) {
                btn.innerHTML = '<i class="fas fa-robot"></i> Disable Auto Sync';
                btn.style.background = '#ef4444';
            } else {
                btn.innerHTML = '<i class="fas fa-robot"></i> Enable Auto Sync';
                btn.style.background = '#667eea';
            }
        }
    }

    loadSettings() {
        // Load current settings
        $('#api-base-url').value = CONFIG.WOOCOMMERCE.BASE_URL || '';
        $('#api-consumer-key').value = CONFIG.WOOCOMMERCE.CONSUMER_KEY || '';
        $('#api-consumer-secret').value = CONFIG.WOOCOMMERCE.CONSUMER_SECRET || '';
        
        $('#site-name').value = CONFIG.SITE.NAME || '';
        $('#contact-email').value = CONFIG.SITE.EMAIL || '';
        $('#phone-number').value = CONFIG.SITE.PHONE || '';
        
        // Update cache info
        const cacheStats = wooAPI.getCacheStats();
        $('#cache-size').textContent = `${cacheStats.size} items`;
    }

    viewProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (product) {
            // Open product modal or new window
            window.open(`/?product=${productId}`, '_blank');
        }
    }

    editProduct(productId) {
        showToast('Product editing will be available in a future update', 'info');
    }

    clearCache() {
        wooAPI.clearCache();
        showToast('Cache cleared successfully', 'success');
        this.loadSettings();
    }

    exportData() {
        const data = {
            products: this.products,
            settings: CONFIG,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `deal4u-export-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        showToast('Data exported successfully', 'success');
    }

    viewLogs() {
        showToast('Detailed logs will be available in a future update', 'info');
    }
}

// Global admin manager instance
const adminManager = new AdminManager();

// Global functions for admin operations
function syncAllProducts() {
    adminManager.syncAllProducts();
}

function fullSync() {
    adminManager.fullSync();
}

function publishedSync() {
    adminManager.publishedSync();
}

function toggleAutoSync() {
    adminManager.toggleAutoSync();
}

function refreshProducts() {
    adminManager.loadProducts();
}

function clearCache() {
    adminManager.clearCache();
}

function exportData() {
    adminManager.exportData();
}

function viewLogs() {
    adminManager.viewLogs();
}

function clearAllCache() {
    adminManager.clearCache();
}

function preloadCache() {
    showToast('Cache preloading started', 'info');
    adminManager.syncAllProducts();
}

function saveApiSettings(event) {
    event.preventDefault();
    showToast('API settings saved (demo mode)', 'success');
}

function saveSiteSettings(event) {
    event.preventDefault();
    showToast('Site settings saved (demo mode)', 'success');
}

// Add admin-specific styles
const adminStyles = `
<style>
.table-actions {
    display: flex;
    gap: 0.5rem;
}

.table-btn {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    color: #6b7280;
}

.table-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.category-tag {
    background: #e0e7ff;
    color: #3730a3;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.products-table th,
.products-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.products-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1f2937;
}

.products-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.products-filters select,
.products-filters input {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
}

.sync-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.sync-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.sync-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 1rem auto 0;
    font-weight: 600;
}

.sync-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.sync-log {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.log-content {
    background: #1f2937;
    color: #d1d5db;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #374151;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #1f2937;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
}

.settings-btn {
    background: #10b981;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
}

.settings-btn:hover {
    background: #059669;
}

.cache-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cache-info {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', adminStyles);
