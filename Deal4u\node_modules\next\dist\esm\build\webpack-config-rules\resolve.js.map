{"version": 3, "sources": ["../../../src/build/webpack-config-rules/resolve.ts"], "names": ["COMPILER_NAMES", "edgeConditionNames", "mainFieldsPerCompiler", "server", "client", "edgeServer", "getMainField", "pageType", "compilerType"], "mappings": "AAAA,SACEA,cAAc,QAET,6BAA4B;AAEnC,0BAA0B;AAC1B,OAAO,MAAMC,qBAAqB;IAChC;IACA;IACA,kCAAkC;IAClC;CACD,CAAA;AAED,MAAMC,wBAGF;IACF,2EAA2E;IAC3E,CAACF,eAAeG,MAAM,CAAC,EAAE;QAAC;QAAQ;KAAS;IAC3C,CAACH,eAAeI,MAAM,CAAC,EAAE;QAAC;QAAW;QAAU;KAAO;IACtD,CAACJ,eAAeK,UAAU,CAAC,EAAEJ;IAC7B,kEAAkE;IAClE,qBAAqB;QAAC;QAAU;KAAO;AACzC;AAEA,OAAO,SAASK,aACdC,QAAyB,EACzBC,YAAgC;IAEhC,IAAIA,iBAAiBR,eAAeK,UAAU,EAAE;QAC9C,OAAOJ;IACT,OAAO,IAAIO,iBAAiBR,eAAeI,MAAM,EAAE;QACjD,OAAOF,qBAAqB,CAACF,eAAeI,MAAM,CAAC;IACrD;IAEA,gFAAgF;IAChF,OAAOG,aAAa,QAChBL,qBAAqB,CAAC,oBAAoB,GAC1CA,qBAAqB,CAACF,eAAeG,MAAM,CAAC;AAClD"}