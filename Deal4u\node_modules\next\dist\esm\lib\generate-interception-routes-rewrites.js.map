{"version": 3, "sources": ["../../src/lib/generate-interception-routes-rewrites.ts"], "names": ["pathToRegexp", "NEXT_URL", "INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "toPathToRegexpPath", "path", "replace", "_", "capture", "startsWith", "slice", "voidParamsBeforeInterceptionMarker", "newPath", "foundInterceptionMarker", "segment", "split", "find", "marker", "push", "join", "generateInterceptionRoutesRewrites", "appPaths", "rewrites", "appPath", "interceptingRoute", "interceptedRoute", "normalizedInterceptingRoute", "normalizedInterceptedRoute", "normalizedAppPath", "interceptingRouteRegex", "toString", "source", "destination", "has", "type", "key", "value"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oCAAmC;AAChE,SAASC,QAAQ,QAAQ,0CAAyC;AAClE,SACEC,0BAA0B,EAC1BC,mCAAmC,EACnCC,0BAA0B,QACrB,+CAA8C;AAGrD,iIAAiI;AACjI,SAASC,mBAAmBC,IAAY;IACtC,OAAOA,KAAKC,OAAO,CAAC,uBAAuB,CAACC,GAAGC;QAC7C,4EAA4E;QAC5E,IAAIA,QAAQC,UAAU,CAAC,QAAQ;YAC7B,OAAO,CAAC,CAAC,EAAED,QAAQE,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC;QACA,OAAO,MAAMF;IACf;AACF;AAEA,gFAAgF;AAChF,yEAAyE;AACzE,2EAA2E;AAC3E,0EAA0E;AAC1E,SAASG,mCAAmCN,IAAY;IACtD,IAAIO,UAAU,EAAE;IAEhB,IAAIC,0BAA0B;IAC9B,KAAK,MAAMC,WAAWT,KAAKU,KAAK,CAAC,KAAM;QACrC,IACEd,2BAA2Be,IAAI,CAAC,CAACC,SAAWH,QAAQL,UAAU,CAACQ,UAC/D;YACAJ,0BAA0B;QAC5B;QAEA,IAAIC,QAAQL,UAAU,CAAC,QAAQ,CAACI,yBAAyB;YACvDD,QAAQM,IAAI,CAAC;QACf,OAAO;YACLN,QAAQM,IAAI,CAACJ;QACf;IACF;IAEA,OAAOF,QAAQO,IAAI,CAAC;AACtB;AAEA,OAAO,SAASC,mCACdC,QAAkB;IAElB,MAAMC,WAAsB,EAAE;IAE9B,KAAK,MAAMC,WAAWF,SAAU;QAC9B,IAAIlB,2BAA2BoB,UAAU;YACvC,MAAM,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAE,GAC3CvB,oCAAoCqB;YAEtC,MAAMG,8BAA8B,CAAC,EACnCF,sBAAsB,MAAMpB,mBAAmBoB,qBAAqB,GACrE,MAAM,CAAC;YAER,MAAMG,6BAA6BvB,mBAAmBqB;YACtD,MAAMG,oBAAoBjB,mCACxBP,mBAAmBmB;YAGrB,qEAAqE;YACrE,4DAA4D;YAC5D,4CAA4C;YAC5C,IAAIM,yBAAyB9B,aAAa2B,6BACvCI,QAAQ,GACRpB,KAAK,CAAC,GAAG,CAAC;YAEbY,SAASJ,IAAI,CAAC;gBACZa,QAAQJ;gBACRK,aAAaJ;gBACbK,KAAK;oBACH;wBACEC,MAAM;wBACNC,KAAKnC;wBACLoC,OAAOP;oBACT;iBACD;YACH;QACF;IACF;IAEA,OAAOP;AACT"}