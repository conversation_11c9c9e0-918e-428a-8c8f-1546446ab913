{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "names": ["bold", "cyan", "green", "red", "yellow", "SimpleWebpackError", "createOriginalStackFrame", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "result", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "line", "start", "column", "source", "rootDirectory", "options", "context", "modulePath", "frame", "originalCodeFrame", "lineNumber", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "getNotFoundError", "name", "message", "errorMessage", "error", "replace", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "err", "getImageError", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AAC3E,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,wBAAwB,QAAQ,6DAA4D;AAGrG,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;YAiBYc,uCAAAA,4BACJA,mCAAAA;QAjBV,MAAMC,MAAMhB,MAAMgB,GAAG,GACjBhB,MAAMgB,GAAG,GACThB,MAAMiB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAChE,MAAMC,iBAAiBtB,MAAMM,MAAM,CAACgB,cAAc;QAElD,MAAMP,SAAS,MAAMjB,yBAAyB;YAC5CyB,MAAMP,IAAIQ,KAAK,CAACD,IAAI;YACpBE,QAAQT,IAAIQ,KAAK,CAACC,MAAM;YACxBC,QAAQJ;YACRK,eAAe1B,YAAY2B,OAAO,CAACC,OAAO;YAC1CC,YAAYhB;YACZiB,OAAO,CAAC;QACV;QAEA,OAAO;YACLA,OAAOhB,CAAAA,0BAAAA,OAAQiB,iBAAiB,KAAI;YACpCC,YAAYlB,CAAAA,2BAAAA,6BAAAA,OAAQmB,kBAAkB,sBAA1BnB,wCAAAA,2BAA4BkB,UAAU,qBAAtClB,sCAAwCoB,QAAQ,OAAM;YAClEV,QAAQV,CAAAA,2BAAAA,8BAAAA,OAAQmB,kBAAkB,sBAA1BnB,oCAAAA,4BAA4BU,MAAM,qBAAlCV,kCAAoCoB,QAAQ,OAAM;QAC5D;IACF,EAAE,OAAM;QACN,OAAO;YAAEJ,OAAO;YAAIE,YAAY;YAAIR,QAAQ;QAAG;IACjD;AACF;AAEA,SAASW,qBACPtB,QAAgB,EAChBR,MAAW,EACX2B,UAAmB,EACnBR,MAAe;QAGbnB;IADF,KACEA,kBAAAA,OAAO+B,OAAO,qBAAd/B,gBAAgBgC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAACpC,OAAOqC,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BtD,KAAKqB;QACrC,IAAImB,cAAcR,QAAQ;YACxBsB,qBAAqB,CAAC,CAAC,EAAEnD,OAAOqC,YAAY,CAAC,EAAErC,OAAO6B,QAAQ,CAAC;QACjE;QAEA,OAAOsB;IACT;AACF;AAEA,OAAO,eAAeC,iBACpB/C,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,MAAW;IAEX,IACEN,MAAMiD,IAAI,KAAK,yBACf,CACEjD,CAAAA,MAAMiD,IAAI,KAAK,sBACf,gCAAgCT,IAAI,CAACxC,MAAMkD,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAEnB,KAAK,EAAEE,UAAU,EAAER,MAAM,EAAE,GAAG,MAAMZ,eAC1Cb,OACAc,UACAb;QAGF,MAAMkD,eAAenD,MAAMoD,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAE3D,MAAM,MAAM,CAAC,CAAC;QAEnE,MAAM4D,cAAc;YAClB,MAAMlD,cAAcL,eAAeC,OAAOC,aACvCiB,GAAG,CAAC,CAAC,EAAET,MAAM,EAAE,GACdA,OAAO8C,kBAAkB,CAACtD,YAAYuD,gBAAgB,GAEvDpC,MAAM,CACL,CAAC6B,OACCA,QACA,CAAC,0FAA0FT,IAAI,CAC7FS,SAEF,CAAC,+BAA+BT,IAAI,CAACS,SACrC,CAAC,mBAAmBT,IAAI,CAACS;YAE/B,IAAI7C,YAAYqD,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAErD,YAAYsD,IAAI,CAAC,MAAM,CAAC;QAC1E;QAEA,IAAIR,UACFvD,IAAIH,KAAK,uBACT,CAAC,EAAE,EAAE2D,aAAa,CAAC,GACnB,OACApB,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACAuB;QAEF,MAAMP,oBAAoBX,qBACxBtB,UACAR,QACA2B,YACAR;QAGF,OAAO,IAAI5B,mBAAmBkD,mBAAmBG;IACnD,EAAE,OAAOS,KAAK;QACZ,8CAA8C;QAC9C,OAAO3D;IACT;AACF;AAEA,OAAO,eAAe4D,cACpB3D,WAAgB,EAChBD,KAAU,EACV2D,GAAU;IAEV,IAAIA,IAAIV,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAM7C,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,MAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,QAAQ;QACtB,OAAO;IACT;IACA,MAAMuD,OAAOpD,OAAOqD,UAAU,CAACT,OAAO,CAAC,uBAAuB;IAC9D,MAAMU,eAAezD,OAAOwD,UAAU;IACtC,MAAMpC,SAASjB,OAAOa,cAAc,GAAG0C,MAAM,GAAG7B,QAAQ,CAAC;IACzD,IAAIF,aAAa,CAAC;IAClBP,OAAOuC,KAAK,CAAC,MAAMC,IAAI,CAAC,CAAC3C;QACvBU;QACA,OAAOV,KAAK4C,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAIlE,mBACT,CAAC,EAAEJ,KAAKoE,MAAM,CAAC,EAAEjE,OAAOqC,WAAWE,QAAQ,IAAI,CAAC,EAChDxC,IAAIH,KAAK,UAAU4E,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH"}