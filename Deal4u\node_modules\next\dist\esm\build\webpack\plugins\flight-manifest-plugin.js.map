{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "names": ["path", "webpack", "sources", "CLIENT_REFERENCE_MANIFEST", "SYSTEM_ENTRYPOINTS", "relative", "getProxiedPluginState", "WEBPACK_LAYERS", "normalizePagePath", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "getDeploymentIdQueryOrEmptyString", "pluginState", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "getAppPathRequiredChunks", "chunkGroup", "excludedFiles", "deploymentIdChunkQuery", "chunks", "for<PERSON>ach", "chunk", "has", "name", "id", "chunkId", "files", "file", "endsWith", "push", "encodeURI", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "PLUGIN_NAME", "ClientReferenceManifestPlugin", "constructor", "options", "dev", "appDir", "appDirBase", "dirname", "sep", "Set", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "configuredCrossOriginLoading", "outputOptions", "crossOriginLoading", "crossOriginMode", "publicPath", "Error", "prefix", "rootMainFiles", "entrypoints", "get", "getFiles", "add", "chunkGroups", "moduleLoading", "crossOrigin", "chunkEntryName", "filter", "f", "startsWith", "requiredChunks", "recordModule", "mod", "layer", "appPagesBrowser", "resource", "type", "_identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "ssrNamedModuleId", "resourceResolveData", "isAsyncModule", "esmResource", "addClientReference", "exportName", "async", "edgeExportName", "addSSRIdMapping", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "request", "includes", "connections", "moduleGraph", "getOutgoingConnections", "connection", "dependency", "clientEntryMod", "getResolvedModule", "modId", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "length", "RawSource"], "mappings": "AAAA;;;;;CAKC,GAED,OAAOA,UAAU,OAAM;AACvB,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,yBAAyB,EACzBC,kBAAkB,QACb,gCAA+B;AACtC,SAASC,QAAQ,QAAQ,OAAM;AAC/B,SAASC,qBAAqB,QAAQ,sBAAqB;AAE3D,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,oCAAoC,QAAQ,gCAA+B;AACpF,SAASC,iCAAiC,QAAQ,sBAAqB;AAgBvE,MAAMC,cAAcL,sBAAsB;IACxCM,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IACtBC,sBAAsB,EAAE;AAC1B;AA4CA,SAASC,yBACPC,UAA8B,EAC9BC,aAA0B;IAE1B,MAAMC,yBAAyBR;IAE/B,MAAMS,SAAwB,EAAE;IAChCH,WAAWG,MAAM,CAACC,OAAO,CAAC,CAACC;QACzB,IAAIjB,mBAAmBkB,GAAG,CAACD,MAAME,IAAI,IAAI,KAAK;YAC5C,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,IAAIF,MAAMG,EAAE,IAAI,MAAM;YACpB,MAAMC,UAAU,KAAKJ,MAAMG,EAAE;YAC7BH,MAAMK,KAAK,CAACN,OAAO,CAAC,CAACO;gBACnB,6DAA6D;gBAC7D,0BAA0B;gBAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;gBAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;gBAC5C,IAAIX,cAAcK,GAAG,CAACK,OAAO,OAAO;gBAEpC,sFAAsF;gBACtF,iFAAiF;gBACjF,iFAAiF;gBACjF,iFAAiF;gBACjF,2DAA2D;gBAC3D,OAAOR,OAAOU,IAAI,CAACJ,SAASK,UAAUH,OAAOT;YAC/C;QACF;IACF;IACA,OAAOC;AACT;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,SAASY,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,MAC/BC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B;IAErC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;AACrE;AAEA,MAAMC,cAAc;AAEpB,OAAO,MAAMC;IAMXC,YAAYC,OAAgB,CAAE;aAL9BC,MAAsB;QAMpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGrD,KAAKsD,OAAO,CAAC,IAAI,CAACF,MAAM,IAAIpD,KAAKuD,GAAG;QACtD,IAAI,CAACzC,oBAAoB,GAAG,IAAI0C,IAAI7C,YAAYG,oBAAoB;IACtE;IAEA2C,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5Bd,aACA,CAACa,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjC/D,QAAQgE,YAAY,CAACC,gBAAgB,EACrCJ;YAEFF,YAAYO,mBAAmB,CAACH,GAAG,CACjC/D,QAAQgE,YAAY,CAACC,gBAAgB,EACrC,IAAIjE,QAAQgE,YAAY,CAACG,cAAc,CAACC,QAAQ;YAElDT,YAAYD,KAAK,CAACW,aAAa,CAACT,GAAG,CACjC;gBACEtC,MAAMwB;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CwB,OAAOtE,QAAQuE,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,WAAW,CAACD,QAAQd,aAAaF,SAASkB,OAAO;QAEtE;IAEJ;IAEAD,YACED,MAAqC,EACrCd,WAAgC,EAChCgB,OAAe,EACf;YAuBAhB;QAtBA,MAAMiB,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvC,MAAMC,+BACJpB,YAAYqB,aAAa,CAACC,kBAAkB;QAC9C,MAAMC,kBACJ,OAAOH,iCAAiC,WACpCA,iCAAiC,oBAC/BA,+BACA,cACF;QAEN,IAAI,OAAOpB,YAAYqB,aAAa,CAACG,UAAU,KAAK,UAAU;YAC5D,MAAM,IAAIC,MACR;QAEJ;QACA,MAAMC,SAAS1B,YAAYqB,aAAa,CAACG,UAAU,IAAI;QAEvD,8EAA8E;QAC9E,8DAA8D;QAC9D,MAAMG,gBAA6B,IAAI/B;SACvCI,+BAAAA,YAAY4B,WAAW,CACpBC,GAAG,CAAChF,0DADPmD,6BAEI8B,QAAQ,GACTtE,OAAO,CAAC,CAACO;YACR,IAAI,oCAAoCU,IAAI,CAACV,OAAO;gBAClD4D,cAAcI,GAAG,CAAChE,KAAKS,OAAO,CAAC,OAAO;YACxC;QACF;QAEFwB,YAAYgC,WAAW,CAACxE,OAAO,CAAC,CAACJ;YAC/B,mEAAmE;YACnE,IAAIgB,YAAY;YAChB,MAAMO,WAAoC;gBACxCsD,eAAe;oBACbP;oBACAQ,aAAaX;gBACf;gBACAvC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,IAAI9B,WAAWO,IAAI,IAAI,YAAYc,IAAI,CAACrB,WAAWO,IAAI,GAAG;gBACxD,sCAAsC;gBACtC,MAAMwE,iBAAiB,AAAC,CAAA,IAAI,CAAC1C,UAAU,GAAGrC,WAAWO,IAAI,AAAD,EAAGa,OAAO,CAChE,UACApC,KAAKuD,GAAG;gBAEVhB,SAASO,aAAa,CAACiD,eAAe,GAAG/E,WACtC0E,QAAQ,GACRM,MAAM,CACL,CAACC,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAErE,QAAQ,CAAC;gBAG5DI,YAAYhB,WAAWO,IAAI;YAC7B;YAEA,MAAM4E,iBAAiBpF,yBAAyBC,YAAYuE;YAC5D,MAAMa,eAAe,CAAC5E,IAAc6E;oBAyBhCA;gBAxBF,0CAA0C;gBAC1C,IAAIA,IAAIC,KAAK,KAAK/F,eAAegG,eAAe,EAAE;oBAChD;gBACF;gBAEA,MAAMC,WACJH,IAAII,IAAI,KAAK,qBAETJ,IAAIK,WAAW,CAACxE,KAAK,CAACmE,IAAIK,WAAW,CAACvE,WAAW,CAAC,OAAO,KACzDkE,IAAIG,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBpE,SAASI,aAAa;gBAC/C,MAAMiE,kBAAkBrE,SAASK,gBAAgB;gBACjD,MAAMiE,sBAAsBtE,SAASM,oBAAoB;gBAEzD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAIiE,mBAAmBzG,SACrBuE,SACAyB,EAAAA,2BAAAA,IAAIU,mBAAmB,qBAAvBV,yBAAyBrG,IAAI,KAAIwG;gBAGnC,IAAI,CAACM,iBAAiBZ,UAAU,CAAC,MAC/BY,mBAAmB,CAAC,EAAE,EAAEA,iBAAiB1E,OAAO,CAAC,OAAO,KAAK,CAAC;gBAEhE,MAAM4E,gBAAgB,IAAI,CAAClG,oBAAoB,CAACQ,GAAG,CAAC+E,IAAIG,QAAQ;gBAEhE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAMS,cAAc,0BAA0B5E,IAAI,CAACmE,YAC/CA,SAASpE,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOpC,KAAKuD,GAAG,KAE3C;gBAEJ,SAAS2D;oBACP,MAAMC,aAAaX;oBACnBjE,SAASI,aAAa,CAACwE,WAAW,GAAG;wBACnC3F;wBACAD,MAAM;wBACNJ,QAAQgF;wBACRiB,OAAOJ;oBACT;oBACA,IAAIC,aAAa;wBACf,MAAMI,iBAAiBJ;wBACvB1E,SAASI,aAAa,CAAC0E,eAAe,GACpC9E,SAASI,aAAa,CAACwE,WAAW;oBACtC;gBACF;gBAEA,SAASG;oBACP,MAAMH,aAAaX;oBACnB,IACE,OAAO7F,YAAYC,eAAe,CAACkG,iBAAiB,KAAK,aACzD;wBACAF,eAAe,CAACpF,GAAG,GAAGoF,eAAe,CAACpF,GAAG,IAAI,CAAC;wBAC9CoF,eAAe,CAACpF,GAAG,CAAC,IAAI,GAAG;4BACzB,GAAGe,SAASI,aAAa,CAACwE,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvChG,QAAQ,EAAE;4BACVK,IAAIb,YAAYC,eAAe,CAACkG,iBAAiB;wBACnD;oBACF;oBAEA,IACE,OAAOnG,YAAYE,mBAAmB,CAACiG,iBAAiB,KACxD,aACA;wBACAD,mBAAmB,CAACrF,GAAG,GAAGqF,mBAAmB,CAACrF,GAAG,IAAI,CAAC;wBACtDqF,mBAAmB,CAACrF,GAAG,CAAC,IAAI,GAAG;4BAC7B,GAAGe,SAASI,aAAa,CAACwE,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvChG,QAAQ,EAAE;4BACVK,IAAIb,YAAYE,mBAAmB,CAACiG,iBAAiB;wBACvD;oBACF;gBACF;gBAEAI;gBACAI;gBAEA/E,SAASI,aAAa,GAAGgE;gBACzBpE,SAASK,gBAAgB,GAAGgE;gBAC5BrE,SAASM,oBAAoB,GAAGgE;YAClC;YAEA,0EAA0E;YAC1E,oEAAoE;YACpE,oEAAoE;YACpE,qDAAqD;YACrD,6CAA6C;YAC7C7F,WAAWG,MAAM,CAACC,OAAO,CAAC,CAACC;gBACzB,MAAMkG,YACJ3D,YAAY4D,UAAU,CAACC,4BAA4B,CAACpG;gBACtD,KAAK,MAAMgF,OAAOkB,UAAW;oBAC3B,IAAIlB,IAAIC,KAAK,KAAK/F,eAAegG,eAAe,EAAE;oBAElD,MAAMmB,UAAU,AAACrB,IAA6BqB,OAAO;oBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;wBACA;oBACF;oBAEA,MAAMC,cACJhE,YAAYiE,WAAW,CAACC,sBAAsB,CAACzB;oBAEjD,KAAK,MAAM0B,cAAcH,YAAa;wBACpC,MAAMI,aAAaD,WAAWC,UAAU;wBACxC,IAAI,CAACA,YAAY;wBAEjB,MAAMC,iBAAiBrE,YAAYiE,WAAW,CAACK,iBAAiB,CAC9DF;wBAEF,MAAMG,QAAQvE,YAAY4D,UAAU,CAACY,WAAW,CAACH;wBAKjD,IAAIE,UAAU,MAAM;4BAClB/B,aAAa+B,OAAOF;wBACtB,OAAO;gCAGHF;4BAFF,oEAAoE;4BACpE,IACEA,EAAAA,qBAAAA,WAAWM,MAAM,qBAAjBN,mBAAmB9E,WAAW,CAAC1B,IAAI,MAAK,sBACxC;gCACA,MAAM+G,kBAAkBP,WAAWM,MAAM;gCACzC,MAAME,oBACJ3E,YAAY4D,UAAU,CAACY,WAAW,CAACE;gCACrClC,aAAamC,mBAAmBN;4BAClC;wBACF;oBACF;gBACF;YACF;YAEA,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoB5F,IAAI,CAACL,YAAY;gBACvC+C,mBAAmBlD,IAAI,CAACG,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,4CAA4C;YAC5C,qBAAqB;YACrB,uBAAuB;YACvB,IAAI,+BAA+BC,IAAI,CAACL,YAAY;gBAClD+C,mBAAmBlD,IAAI,CAAC,IAAI,CAACsB,GAAG,GAAG,kBAAkB;YACvD;YAEA,MAAMlB,YAAYF,qBAAqBC;YACvC,IAAI,CAAC6C,kBAAkBvD,GAAG,CAACW,YAAY;gBACrC4C,kBAAkBb,GAAG,CAAC/B,WAAW,EAAE;YACrC;YACA4C,kBAAkBY,GAAG,CAACxD,WAAYJ,IAAI,CAACU;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAMiG,YAAYzD,mBAAoB;YACzC,MAAM0D,iBAA0C;gBAC9C5C,eAAe;oBACbP;oBACAQ,aAAaX;gBACf;gBACAvC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,MAAM4F,WAAW;mBAAI3G,qBAAqByG,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAMnG,YAAYsC,kBAAkBY,GAAG,CAACmD,UAAU,EAAE,CAAE;oBACzDtG,cAAcmG,gBAAgBlG;gBAChC;gBACAqG,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAASpG,OAAO,CAAC,QAAQ;YAC1C,MAAM8G,iBAAiB1I,kBAAkByI,SAAS/G,KAAK,CAAC,MAAMiH,MAAM;YACpEzE,MAAM,CACJ,eAAewE,iBAAiB,MAAM/I,4BAA4B,MACnE,GAAG,IAAID,QAAQkJ,SAAS,CACvB,CAAC,oFAAoF,EAAEL,KAAKC,SAAS,CACnGC,SAAS/G,KAAK,CAAC,MAAMiH,MAAM,GAC3B,EAAE,EAAEL,KAAK,CAAC;YAGd,IAAIG,aAAa,iBAAiB;gBAChC,kEAAkE;gBAClEvE,MAAM,CAAC,2BAA2BvE,4BAA4B,MAAM,GAClE,IAAID,QAAQkJ,SAAS,CACnB,CAAC,oFAAoF,EAAEL,KAAKC,SAAS,CACnG,eACA,EAAE,EAAEF,KAAK,CAAC;YAElB;QACF;QAEAnI,YAAYG,oBAAoB,GAAG,EAAE;IACvC;AACF"}