{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["RSC_MOD_REF_PROXY_ALIAS", "WEBPACK_LAYERS", "RSC_MODULE_TYPES", "warnOnce", "getRSCModuleInformation", "getModuleBuildInfo", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "transformSource", "source", "sourceMap", "buildInfo", "Error", "_module", "rsc", "type", "client", "issuer<PERSON><PERSON>er", "layer", "sourceType", "parser", "detectedClientEntryType", "clientEntryType", "clientRefs", "<PERSON><PERSON><PERSON><PERSON>", "resourcePath", "includes", "callback", "assumedSourceType", "length", "esmSource", "cnt", "ref", "replace"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,cAAc,QACT,4BAA2B;AAClC,SAASC,gBAAgB,QAAQ,mCAAkC;AACnE,SAASC,QAAQ,QAAQ,yCAAwC;AACjE,SAASC,uBAAuB,QAAQ,yCAAwC;AAChF,SAASC,kBAAkB,QAAQ,2BAA0B;AAE7D,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEF,eAAe,SAASC,gBAEtBC,MAAc,EACdC,SAAc;QAaVC,gBAsFAA;IAjGJ,8BAA8B;IAC9B,IAAI,OAAOF,WAAW,UAAU;QAC9B,MAAM,IAAIG,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMD,YAAYR,mBAAmB,IAAI,CAACU,OAAO;IACjDF,UAAUG,GAAG,GAAGZ,wBAAwBO,QAAQ;IAEhD,qBAAqB;IACrB,IAAIE,EAAAA,iBAAAA,UAAUG,GAAG,qBAAbH,eAAeI,IAAI,MAAKf,iBAAiBgB,MAAM,EAAE;YAEhC,sBAAA;QADnB,MAAMC,cAAc,IAAI,CAACJ,OAAO,CAACK,KAAK;QACtC,MAAMC,cAAa,gBAAA,IAAI,CAACN,OAAO,sBAAZ,uBAAA,cAAcO,MAAM,qBAApB,qBAAsBD,UAAU;QACnD,MAAME,0BAA0BV,UAAUG,GAAG,CAACQ,eAAe;QAC7D,MAAMC,aAAaZ,UAAUG,GAAG,CAACS,UAAU;QAE3C,IAAIN,gBAAgBlB,eAAeyB,aAAa,EAAE;YAChD,8EAA8E;YAC9E,2EAA2E;YAC3E,4EAA4E;YAC5E,SAAS;YAET,+EAA+E;YAC/E,2EAA2E;YAC3E,6EAA6E;YAC7E,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAACC,YAAY,CAACC,QAAQ,CAAC,iBAAiB;gBAC/C,IAAI,CAACC,QAAQ,CACX,IAAIf,MACF,CAAC,sUAAsU,CAAC;gBAG5U;YACF;QACF;QAEA,4EAA4E;QAC5E,6EAA6E;QAC7E,4DAA4D;QAC5D,IAAIgB,oBAAoBT;QACxB,IAAIS,sBAAsB,UAAUP,4BAA4B,QAAQ;YACtE,IACEE,WAAWM,MAAM,KAAK,KACrBN,WAAWM,MAAM,KAAK,KAAKN,UAAU,CAAC,EAAE,KAAK,IAC9C;gBACA,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpBK,oBAAoB;YACtB,OAAO,IAAI,CAACL,WAAWG,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3CE,oBAAoB;YACtB;QACF;QAEA,IAAIA,sBAAsB,UAAU;YAClC,IAAIL,WAAWG,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACC,QAAQ,CACX,IAAIf,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIkB,YAAY,CAAC;6BACM,EAAEvB,kBAAkB;sCACX,EAAE,IAAI,CAACkB,YAAY,CAAC;;;;;;;;AAQ1D,CAAC;YACK,IAAIM,MAAM;YACV,KAAK,MAAMC,OAAOT,WAAY;gBAC5B,IAAIS,QAAQ,IAAI;oBACdF,aAAa,CAAC,wCAAwC,EAAE,IAAI,CAACL,YAAY,CAAC,KAAK,CAAC;gBAClF,OAAO,IAAIO,QAAQ,WAAW;oBAC5BF,aAAa,CAAC;;2BAEG,CAAC;gBACpB,OAAO;oBACLA,aAAa,CAAC;OACjB,EAAEC,IAAI,2BAA2B,EAAE,IAAI,CAACN,YAAY,CAAC,CAAC,EAAEO,IAAI;UACzD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACL,QAAQ,CAAC,MAAMG,WAAWpB;YAC/B;QACF;IACF;IAEA,IAAIC,EAAAA,kBAAAA,UAAUG,GAAG,qBAAbH,gBAAeI,IAAI,MAAKf,iBAAiBgB,MAAM,EAAE;QACnD,IAAIZ,iBAAiB,IAAI,CAACqB,YAAY,EAAE;YACtCxB,SACE,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,IAAI,CAAC0B,QAAQ,CACX,MACAlB,OAAOwB,OAAO,CAACnC,yBAAyBS,oBACxCG;AAEJ"}