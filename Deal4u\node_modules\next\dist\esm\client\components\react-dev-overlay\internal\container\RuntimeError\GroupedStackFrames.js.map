{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/GroupedStackFrames.tsx"], "names": ["React", "CallStackFrame", "FrameworkIcon", "FrameworkGroup", "framework", "stackFrames", "all", "details", "data-nextjs-collapsed-call-stack-details", "summary", "tabIndex", "svg", "data-nextjs-call-stack-chevron-icon", "fill", "height", "width", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "path", "d", "map", "frame", "index", "key", "GroupedStackFrames", "groupedStackFrames", "stackFramesGroup", "groupIndex", "frameIndex"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,SAASC,cAAc,QAAQ,mBAAkB;AACjD,SAASC,aAAa,QAAQ,kBAAiB;AAE/C,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,SAAS,EACTC,WAAW,EACXC,GAAG,EAKJ,GARuB;IAStB,qBACE,wDACE,oBAACC;QAAQC,4CAAAA;qBACP,oBAACC;QACCC,UAAU;qBAEV,oBAACC;QACCC,uCAAAA;QACAC,MAAK;QACLC,QAAO;QACPC,OAAM;QACNC,gBAAe;QACfC,QAAO;QACPC,eAAc;QACdC,gBAAe;QACfC,aAAY;QACZC,SAAQ;qBAER,oBAACC;QAAKC,GAAE;uBAEV,oBAACrB;QAAcE,WAAWA;QACzBA,cAAc,UAAU,UAAU,YAGpCC,YAAYmB,GAAG,CAAC,CAACC,OAAOC,sBACvB,oBAACzB;YAAe0B,KAAK,AAAC,gBAAaD,QAAM,MAAGpB;YAAOmB,OAAOA;;AAKpE;AAEA,OAAO,SAASG,mBAAmB,KAMlC;IANkC,IAAA,EACjCC,kBAAkB,EAClBvB,GAAG,EAIJ,GANkC;IAOjC,qBACE,0CACGuB,mBAAmBL,GAAG,CAAC,CAACM,kBAAkBC;QACzC,oCAAoC;QACpC,IAAID,iBAAiB1B,SAAS,EAAE;YAC9B,qBACE,oBAACD;gBACCwB,KAAK,AAAC,gCAA6BI,aAAW,MAAGzB;gBACjDF,WAAW0B,iBAAiB1B,SAAS;gBACrCC,aAAayB,iBAAiBzB,WAAW;gBACzCC,KAAKA;;QAGX;QAEA,OACE,2CAA2C;QAC3CwB,iBAAiBzB,WAAW,CAACmB,GAAG,CAAC,CAACC,OAAOO,2BACvC,oBAAC/B;gBACC0B,KAAK,AAAC,gBAAaI,aAAW,MAAGC,aAAW,MAAG1B;gBAC/CmB,OAAOA;;IAIf;AAGN"}