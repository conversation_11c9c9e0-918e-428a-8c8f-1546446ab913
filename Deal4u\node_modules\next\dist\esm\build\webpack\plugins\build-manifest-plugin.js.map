{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "names": ["devalue", "webpack", "sources", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "SYSTEM_ENTRYPOINTS", "getRouteFromEntrypoint", "ampFirstEntryNamesMap", "getSortedRoutes", "spans", "srcEmptySsgManifest", "normalizeRewrite", "item", "has", "source", "destination", "normalizeRewritesForBuildManifest", "rewrites", "afterFiles", "map", "beforeFiles", "fallback", "generateClientManifest", "compiler", "compilation", "assetMap", "compilationSpan", "get", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "appDependencies", "Set", "pages", "sortedPageKeys", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "getEntrypointFiles", "entrypoint", "getFiles", "file", "test", "replace", "processRoute", "r", "rewrite", "startsWith", "BuildManifestPlugin", "constructor", "options", "buildId", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "exportRuntime", "createAssets", "assets", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "lowPriorityFiles", "rootMainFiles", "ampFirstPages", "ampFirstEntryNames", "entryName", "pagePath", "push", "mainFiles", "compilationAssets", "getAssets", "p", "name", "endsWith", "info", "v", "values", "filesForPage", "ssgManifestPath", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "JSON", "stringify", "clientManifestPath", "apply", "hooks", "make", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AACA,OAAOA,aAAa,6BAA4B;AAChD,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,cAAc,EACdC,yBAAyB,EACzBC,wBAAwB,EACxBC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,+BAA+B,EAC/BC,kBAAkB,QACb,gCAA+B;AAEtC,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,KAAK,QAAQ,qBAAoB;AAQ1C,sEAAsE;AACtE,kEAAkE;AAClE,cAAc;AACd,OAAO,MAAMC,sBAAsB,CAAC,4EAA4E,CAAC,CAAA;AAEjH,SAASC,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEA,OAAO,SAASC,kCACdC,QAAkC;QAGpBA,sBACCA,uBACHA;IAHZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,qBAAnBD,qBAAqBE,GAAG,CAAC,CAACP,OAASD,iBAAiBC;QAChEQ,WAAW,GAAEH,wBAAAA,SAASG,WAAW,qBAApBH,sBAAsBE,GAAG,CAAC,CAACP,OAASD,iBAAiBC;QAClES,QAAQ,GAAEJ,qBAAAA,SAASI,QAAQ,qBAAjBJ,mBAAmBE,GAAG,CAAC,CAACP,OAASD,iBAAiBC;IAC9D;AACF;AAEA,mFAAmF;AACnF,yCAAyC;AACzC,SAASU,uBACPC,QAAa,EACbC,WAAgB,EAChBC,QAAuB,EACvBR,QAAkC;IAElC,MAAMS,kBAAkBjB,MAAMkB,GAAG,CAACH,gBAAgBf,MAAMkB,GAAG,CAACJ;IAC5D,MAAMK,wBAAwBF,mCAAAA,gBAAiBG,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYhB,kCAAkCC;QAChD;QACA,MAAMgB,kBAAkB,IAAIC,IAAIT,SAASU,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiB5B,gBAAgB6B,OAAOC,IAAI,CAACb,SAASU,KAAK;QAEjEC,eAAeG,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAehB,SAASU,KAAK,CAACK,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACX,gBAAgBpB,GAAG,CAAC+B;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBd,cAAc,CAACS,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEX,eAAee,WAAW,GAAGV;QAE7B,OAAO1C,QAAQqC;IACjB;AACF;AAEA,OAAO,SAASgB,mBAAmBC,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACC/B,GAAG,CAAC,CAAC+B,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEA,MAAMC,eAAe,CAACC;IACpB,MAAMC,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,CAACC,QAAQxC,WAAW,CAACyC,UAAU,CAAC,MAAM;QACxC,OAAO,AAACD,QAAgBxC,WAAW;IACrC;IACA,OAAOwC;AACT;AAEA,iFAAiF;AACjF,+GAA+G;AAC/G,eAAe,MAAME;IAOnBC,YAAYC,OAMX,CAAE;QACD,IAAI,CAACC,OAAO,GAAGD,QAAQC,OAAO;QAC9B,IAAI,CAACC,aAAa,GAAG,CAAC,CAACF,QAAQE,aAAa;QAC5C,IAAI,CAAC5C,QAAQ,GAAG;YACdG,aAAa,EAAE;YACfF,YAAY,EAAE;YACdG,UAAU,EAAE;QACd;QACA,IAAI,CAACyC,aAAa,GAAGH,QAAQG,aAAa;QAC1C,IAAI,CAAC7C,QAAQ,CAACG,WAAW,GAAGuC,QAAQ1C,QAAQ,CAACG,WAAW,CAACD,GAAG,CAACkC;QAC7D,IAAI,CAACpC,QAAQ,CAACC,UAAU,GAAGyC,QAAQ1C,QAAQ,CAACC,UAAU,CAACC,GAAG,CAACkC;QAC3D,IAAI,CAACpC,QAAQ,CAACI,QAAQ,GAAGsC,QAAQ1C,QAAQ,CAACI,QAAQ,CAACF,GAAG,CAACkC;QACvD,IAAI,CAACU,aAAa,GAAG,CAAC,CAACJ,QAAQI,aAAa;IAC9C;IAEAC,aAAazC,QAAa,EAAEC,WAAgB,EAAEyC,MAAW,EAAE;QACzD,MAAMvC,kBAAkBjB,MAAMkB,GAAG,CAACH,gBAAgBf,MAAMkB,GAAG,CAACJ;QAC5D,MAAM2C,mBAAmBxC,mCAAAA,gBAAiBG,UAAU,CAClD;QAEF,OAAOqC,oCAAAA,iBAAkBpC,OAAO,CAAC;YAC/B,MAAMqC,cAAgC3C,YAAY2C,WAAW;YAC7D,MAAM1C,WAAuC;gBAC3C2C,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACfC,kBAAkB,EAAE;gBACpBC,eAAe,EAAE;gBACjBrC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBsC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBnE,sBAAsBoB,GAAG,CAACH;YACrD,IAAIkD,oBAAoB;gBACtB,KAAK,MAAMC,aAAaD,mBAAoB;oBAC1C,MAAME,WAAWtE,uBAAuBqE;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEAnD,SAASgD,aAAa,CAACI,IAAI,CAACD;gBAC9B;YACF;YAEA,MAAME,YAAY,IAAI5C,IACpBa,mBAAmBoB,YAAYxC,GAAG,CAAC3B;YAGrC,IAAI,IAAI,CAAC8D,aAAa,EAAE;gBACtBrC,SAAS+C,aAAa,GAAG;uBACpB,IAAItC,IACLa,mBACEoB,YAAYxC,GAAG,CAAC1B;iBAGrB;YACH;YAEA,MAAM8E,oBAIAvD,YAAYwD,SAAS;YAE3BvD,SAAS2C,aAAa,GAAGW,kBACtBpC,MAAM,CAAC,CAACsC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAEC,IAAI,CAACC,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACEF,EAAEG,IAAI,IAAIlF,gDAAgD+E,EAAEG,IAAI;YAEpE,GACCjE,GAAG,CAAC,CAACkE,IAAMA,EAAEH,IAAI;YAEpBzD,SAAS4C,QAAQ,GAAGtB,mBAClBoB,YAAYxC,GAAG,CAACxB,4CAChBwC,MAAM,CAAC,CAACO,OAAS,CAAC4B,UAAUjE,GAAG,CAACqC;YAElCzB,SAAS6C,WAAW,GAAGvB,mBACrBoB,YAAYxC,GAAG,CAACvB;YAGlB,KAAK,MAAM4C,cAAcxB,YAAY2C,WAAW,CAACmB,MAAM,GAAI;gBACzD,IAAIjF,mBAAmBQ,GAAG,CAACmC,WAAWkC,IAAI,GAAG;gBAC7C,MAAMN,WAAWtE,uBAAuB0C,WAAWkC,IAAI;gBAEvD,IAAI,CAACN,UAAU;oBACb;gBACF;gBAEA,MAAMW,eAAexC,mBAAmBC;gBAExCvB,SAASU,KAAK,CAACyC,SAAS,GAAG;uBAAI,IAAI1C,IAAI;2BAAI4C;2BAAcS;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAAC1B,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5BpC,SAAS8C,gBAAgB,CAACM,IAAI,CAC5B,CAAC,EAAE9E,yBAAyB,CAAC,EAAE,IAAI,CAAC6D,OAAO,CAAC,kBAAkB,CAAC;gBAEjE,MAAM4B,kBAAkB,CAAC,EAAEzF,yBAAyB,CAAC,EAAE,IAAI,CAAC6D,OAAO,CAAC,gBAAgB,CAAC;gBAErFnC,SAAS8C,gBAAgB,CAACM,IAAI,CAACW;gBAC/BvB,MAAM,CAACuB,gBAAgB,GAAG,IAAI5F,QAAQ6F,SAAS,CAAC/E;YAClD;YAEAe,SAASU,KAAK,GAAGE,OAAOC,IAAI,CAACb,SAASU,KAAK,EACxCuD,IAAI,EACL,2BAA2B;aAC1BC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGpE,SAASU,KAAK,CAAC0D,EAAE,EAAGD,CAAAA,GAAI,CAAC;YAEtD,IAAIE,oBAAoBjG;YAExB,IAAI,IAAI,CAACgE,aAAa,EAAE;gBACtBiC,oBAAoB,CAAC,SAAS,EAAEjG,eAAe,CAAC;YAClD;YAEAoE,MAAM,CAAC6B,kBAAkB,GAAG,IAAIlG,QAAQ6F,SAAS,CAC/CM,KAAKC,SAAS,CAACvE,UAAU,MAAM;YAGjC,IAAI,IAAI,CAACsC,aAAa,EAAE;gBACtBE,MAAM,CAAC,CAAC,OAAO,EAAEnE,0BAA0B,GAAG,CAAC,CAAC,GAC9C,IAAIF,QAAQ6F,SAAS,CACnB,CAAC,sBAAsB,EAAEM,KAAKC,SAAS,CAACvE,UAAU,CAAC;YAEzD;YAEA,IAAI,CAAC,IAAI,CAACoC,aAAa,EAAE;gBACvB,MAAMoC,qBAAqB,CAAC,EAAElG,yBAAyB,CAAC,EAAE,IAAI,CAAC6D,OAAO,CAAC,kBAAkB,CAAC;gBAE1FK,MAAM,CAACgC,mBAAmB,GAAG,IAAIrG,QAAQ6F,SAAS,CAChD,CAAC,wBAAwB,EAAEnE,uBACzBC,UACAC,aACAC,UACA,IAAI,CAACR,QAAQ,EACb,uDAAuD,CAAC;YAE9D;YAEA,OAAOgD;QACT;IACF;IAEAiC,MAAM3E,QAA0B,EAAE;QAChCA,SAAS4E,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAAC7E;YAC9CA,YAAY2E,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEnB,MAAM;gBACNqB,OAAO5G,QAAQ6G,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACxC;gBACC,IAAI,CAACD,YAAY,CAACzC,UAAUC,aAAayC;YAC3C;QAEJ;QACA;IACF;AACF"}