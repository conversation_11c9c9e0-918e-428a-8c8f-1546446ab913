{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts"], "names": ["createHrefFromUrl", "fetchServerResponse", "PrefetchKind", "createRecordFromThenable", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "NEXT_RSC_UNION_QUERY", "PromiseQueue", "prefetchQueue", "prefetchReducer", "state", "action", "prefetchCache", "url", "searchParams", "delete", "href", "cacheEntry", "get", "kind", "TEMPORARY", "set", "AUTO", "FULL", "serverResponse", "enqueue", "tree", "nextUrl", "buildId", "treeAtTimeOfPrefetch", "data", "prefetchTime", "Date", "now", "lastUsedTime"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,mBAAmB,QAAQ,2BAA0B;AAM9D,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,oBAAoB,QAAQ,2BAA0B;AAC/D,SAASC,YAAY,QAAQ,sBAAqB;AAElD,OAAO,MAAMC,gBAAgB,IAAID,aAAa,GAAE;AAEhD,OAAO,SAASE,gBACdC,KAA2B,EAC3BC,MAAsB;IAEtB,4DAA4D;IAC5DN,mBAAmBK,MAAME,aAAa;IAEtC,MAAM,EAAEC,GAAG,EAAE,GAAGF;IAChBE,IAAIC,YAAY,CAACC,MAAM,CAACT;IAExB,MAAMU,OAAOf,kBACXY,KACA,0FAA0F;IAC1F;IAGF,MAAMI,aAAaP,MAAME,aAAa,CAACM,GAAG,CAACF;IAC3C,IAAIC,YAAY;QACd;;;KAGC,GACD,IAAIA,WAAWE,IAAI,KAAKhB,aAAaiB,SAAS,EAAE;YAC9CV,MAAME,aAAa,CAACS,GAAG,CAACL,MAAM;gBAC5B,GAAGC,UAAU;gBACbE,MAAMR,OAAOQ,IAAI;YACnB;QACF;QAEA;;;MAGE,GACF,IACE,CACEF,CAAAA,WAAWE,IAAI,KAAKhB,aAAamB,IAAI,IACrCX,OAAOQ,IAAI,KAAKhB,aAAaoB,IAAI,AAAD,GAElC;YACA,OAAOb;QACT;IACF;IAEA,uGAAuG;IACvG,MAAMc,iBAAiBpB,yBACrBI,cAAciB,OAAO,CAAC,IACpBvB,oBACEW,KACA,mKAAmK;QACnKH,MAAMgB,IAAI,EACVhB,MAAMiB,OAAO,EACbjB,MAAMkB,OAAO,EACbjB,OAAOQ,IAAI;IAKjB,wEAAwE;IACxET,MAAME,aAAa,CAACS,GAAG,CAACL,MAAM;QAC5B,wEAAwE;QACxEa,sBAAsBnB,MAAMgB,IAAI;QAChCI,MAAMN;QACNL,MAAMR,OAAOQ,IAAI;QACjBY,cAAcC,KAAKC,GAAG;QACtBC,cAAc;IAChB;IAEA,OAAOxB;AACT"}