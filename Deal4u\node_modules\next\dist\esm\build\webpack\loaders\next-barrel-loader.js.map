{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-barrel-loader.ts"], "names": ["path", "transform", "WEBPACK_LAYERS", "barrelTransformMappingCache", "Map", "getBarrelMapping", "layer", "resourcePath", "swcCacheDir", "resolve", "fs", "has", "get", "transpileSource", "filename", "source", "isWildcard", "isTypeScript", "endsWith", "Promise", "res", "inputSourceMap", "undefined", "sourceFileName", "optimizeBarrelExports", "wildcard", "serverComponents", "isReactServerLayer", "reactServerComponents", "jsc", "parser", "syntax", "experimental", "cacheRoot", "then", "output", "code", "visited", "Set", "getMatches", "file", "add", "rej", "readFile", "err", "data", "toString", "matches", "match", "prefix", "exportList", "JSON", "parse", "slice", "wildcardExports", "matchAll", "map", "decl", "length", "all", "req", "targetPath", "dirname", "replace", "targetMatches", "concat", "set", "NextBarrelLoader", "async", "cacheable", "names", "getOptions", "getResolve", "mainFields", "mapping", "_module", "clearDependencies", "callback", "stringify", "exportMap", "name", "filePath", "orig", "missedNames", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC,GAID,OAAOA,UAAU,OAAM;AACvB,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,cAAc,QAAQ,yBAAwB;AAEvD,iFAAiF;AACjF,mFAAmF;AACnF,+DAA+D;AAC/D,kEAAkE;AAClE,MAAMC,8BAA8B,IAAIC;AASxC,eAAeC,iBACbC,KAAgC,EAChCC,YAAoB,EACpBC,WAAmB,EACnBC,OAA8D,EAC9DC,EAKC;IAED,IAAIP,4BAA4BQ,GAAG,CAACJ,eAAe;QACjD,OAAOJ,4BAA4BS,GAAG,CAACL;IACzC;IAEA,6EAA6E;IAC7E,mDAAmD;IACnD,eAAeM,gBACbC,QAAgB,EAChBC,MAAc,EACdC,UAAmB;QAEnB,MAAMC,eAAeH,SAASI,QAAQ,CAAC,UAAUJ,SAASI,QAAQ,CAAC;QACnE,OAAO,IAAIC,QAAgB,CAACC,MAC1BnB,UAAUc,QAAQ;gBAChBD;gBACAO,gBAAgBC;gBAChBC,gBAAgBT;gBAChBU,uBAAuB;oBACrBC,UAAUT;gBACZ;gBACAU,kBAAkB;oBAChBC,oBAAoBrB,UAAUJ,eAAe0B,qBAAqB;gBACpE;gBACAC,KAAK;oBACHC,QAAQ;wBACNC,QAAQd,eAAe,eAAe;wBACtC,CAACA,eAAe,QAAQ,MAAM,EAAE;oBAClC;oBACAe,cAAc;wBACZC,WAAWzB;oBACb;gBACF;YACF,GAAG0B,IAAI,CAAC,CAACC;gBACPf,IAAIe,OAAOC,IAAI;YACjB;IAEJ;IAEA,yCAAyC;IACzC,MAAMC,UAAU,IAAIC;IACpB,eAAeC,WAAWC,IAAY,EAAExB,UAAmB;QACzD,IAAIqB,QAAQ1B,GAAG,CAAC6B,OAAO;YACrB,OAAO;QACT;QACAH,QAAQI,GAAG,CAACD;QAEZ,MAAMzB,SAAS,MAAM,IAAII,QAAgB,CAACC,KAAKsB;YAC7ChC,GAAGiC,QAAQ,CAACH,MAAM,CAACI,KAAKC;gBACtB,IAAID,OAAOC,SAASvB,WAAW;oBAC7BoB,IAAIE;gBACN,OAAO;oBACLxB,IAAIyB,KAAKC,QAAQ;gBACnB;YACF;QACF;QAEA,MAAMX,SAAS,MAAMtB,gBAAgB2B,MAAMzB,QAAQC;QAEnD,MAAM+B,UAAUZ,OAAOa,KAAK,CAC1B;QAEF,IAAI,CAACD,SAAS;YACZ,OAAO;QACT;QAEA,MAAME,SAASF,OAAO,CAAC,EAAE;QACzB,IAAIG,aAAaC,KAAKC,KAAK,CAACL,OAAO,CAAC,EAAE,CAACM,KAAK,CAAC,GAAG,CAAC;QAKjD,MAAMC,kBAAkB;eACnBnB,OAAOoB,QAAQ,CAAC;SACpB,CAACC,GAAG,CAAC,CAACR,QAAUA,KAAK,CAAC,EAAE;QAEzB,uEAAuE;QACvE,sEAAsE;QACtE,eAAe;QACf,IAAIhC,YAAY;YACd,KAAK,MAAMyC,QAAQP,WAAY;gBAC7BO,IAAI,CAAC,EAAE,GAAGjB;gBACViB,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;YACnB;QACF;QAEA,6EAA6E;QAC7E,IAAIH,gBAAgBI,MAAM,EAAE;YAC1B,MAAMvC,QAAQwC,GAAG,CACfL,gBAAgBE,GAAG,CAAC,OAAOI;gBACzB,MAAMC,aAAa,MAAMpD,QACvBT,KAAK8D,OAAO,CAACtB,OACboB,IAAIG,OAAO,CAAC,gDAAgD;gBAG9D,MAAMC,gBAAgB,MAAMzB,WAAWsB,YAAY;gBACnD,IAAIG,eAAe;oBACjB,wBAAwB;oBACxBd,aAAaA,WAAWe,MAAM,CAACD,cAAcd,UAAU;gBACzD;YACF;QAEJ;QAEA,OAAO;YACLD;YACAC;YACAI;QACF;IACF;IAEA,MAAMlC,MAAM,MAAMmB,WAAWhC,cAAc;IAC3CJ,4BAA4B+D,GAAG,CAAC3D,cAAca;IAE9C,OAAOA;AACT;AAEA,MAAM+C,mBAAmB;QAkBrB;IAZF,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,SAAS,CAAC;IAEf,MAAM,EAAEC,KAAK,EAAE9D,WAAW,EAAE,GAAG,IAAI,CAAC+D,UAAU;IAE9C,yEAAyE;IACzE,6EAA6E;IAC7E,MAAM9D,UAAU,IAAI,CAAC+D,UAAU,CAAC;QAC9BC,YAAY;YAAC;YAAU;SAAO;IAChC;IAEA,MAAMC,UAAU,MAAMrE,kBACpB,gBAAA,IAAI,CAACsE,OAAO,qBAAZ,cAAcrE,KAAK,EACnB,IAAI,CAACC,YAAY,EACjBC,aACAC,SACA,IAAI,CAACC,EAAE;IAGT,4EAA4E;IAC5E,yEAAyE;IACzE,6EAA6E;IAC7E,wBAAwB;IACxB,IAAI,CAACkE,iBAAiB;IAEtB,IAAI,CAACF,SAAS;QACZ,6FAA6F;QAC7F,2FAA2F;QAC3F,0FAA0F;QAC1F,+BAA+B;QAC/B,IAAI,CAACG,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE1B,KAAK2B,SAAS,CAAC,IAAI,CAACvE,YAAY,EAAE,CAAC;QACxE;IACF;IAEA,6EAA6E;IAC7E,MAAM0C,SAASyB,QAAQzB,MAAM;IAC7B,MAAMC,aAAawB,QAAQxB,UAAU;IACrC,MAAM6B,YAAY,IAAI3E;IACtB,KAAK,MAAM,CAAC4E,MAAMC,UAAUC,KAAK,IAAIhC,WAAY;QAC/C6B,UAAUb,GAAG,CAACc,MAAM;YAACC;YAAUC;SAAK;IACtC;IAEA,IAAI/C,SAASc;IACb,IAAIkC,cAAwB,EAAE;IAC9B,KAAK,MAAMH,QAAQV,MAAO;QACxB,sBAAsB;QACtB,IAAIS,UAAUpE,GAAG,CAACqE,OAAO;YACvB,MAAMvB,OAAOsB,UAAUnE,GAAG,CAACoE;YAE3B,IAAIvB,IAAI,CAAC,EAAE,KAAK,KAAK;gBACnBtB,UAAU,CAAC,cAAc,EAAE6C,KAAK,MAAM,EAAE7B,KAAK2B,SAAS,CAACrB,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAK,WAAW;gBAChCtB,UAAU,CAAC,sBAAsB,EAAE6C,KAAK,QAAQ,EAAE7B,KAAK2B,SAAS,CAC9DrB,IAAI,CAAC,EAAE,EACP,CAAC;YACL,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAKuB,MAAM;gBAC3B7C,UAAU,CAAC,WAAW,EAAE6C,KAAK,QAAQ,EAAE7B,KAAK2B,SAAS,CAACrB,IAAI,CAAC,EAAE,EAAE,CAAC;YAClE,OAAO;gBACLtB,UAAU,CAAC,WAAW,EAAEsB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAEuB,KAAK,QAAQ,EAAE7B,KAAK2B,SAAS,CACjErB,IAAI,CAAC,EAAE,EACP,CAAC;YACL;QACF,OAAO;YACL0B,YAAYC,IAAI,CAACJ;QACnB;IACF;IAEA,mCAAmC;IACnC,IAAIG,YAAYzB,MAAM,GAAG,GAAG;QAC1B,KAAK,MAAME,OAAOc,QAAQpB,eAAe,CAAE;YACzCnB,UAAU,CAAC,gBAAgB,EAAEgB,KAAK2B,SAAS,CACzClB,IAAIG,OAAO,CAAC,mBAAmBoB,YAAYE,IAAI,CAAC,OAAO,cACvD,CAAC;QACL;IACF;IAEA,IAAI,CAACR,QAAQ,CAAC,MAAM1C;AACtB;AAEA,eAAegC,iBAAgB"}