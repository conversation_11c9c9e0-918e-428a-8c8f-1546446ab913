{"version": 3, "sources": ["../../../src/lib/typescript/writeAppTypeDeclarations.ts"], "names": ["os", "path", "promises", "fs", "writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "hasPagesDir", "isAppDirEnabled", "appTypeDeclarations", "join", "eol", "EOL", "currentC<PERSON>nt", "readFile", "lf", "indexOf", "directives", "push", "content", "writeFile"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,YAAYC,EAAE,QAAQ,KAAI;AAEnC,OAAO,eAAeC,yBAAyB,EAC7CC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,eAAe,EAMhB;IACC,yBAAyB;IACzB,MAAMC,sBAAsBR,KAAKS,IAAI,CAACL,SAAS;IAE/C,iCAAiC;IACjC,IAAIM,MAAMX,GAAGY,GAAG;IAChB,IAAIC;IAEJ,IAAI;QACFA,iBAAiB,MAAMV,GAAGW,QAAQ,CAACL,qBAAqB;QACxD,uDAAuD;QACvD,MAAMM,KAAKF,eAAeG,OAAO,CAAC,MAAM,+BAA+B,GAAG;QAE1E,IAAID,OAAO,CAAC,GAAG;YACb,IAAIF,cAAc,CAACE,KAAK,EAAE,KAAK,MAAM;gBACnCJ,MAAM;YACR,OAAO;gBACLA,MAAM;YACR;QACF;IACF,EAAE,OAAM,CAAC;IAET;;;;;GAKC,GACD,MAAMM,aAAuB;QAC3B,oCAAoC;QACpC;KACD;IAED,IAAIX,qBAAqB;QACvBW,WAAWC,IAAI,CAAC;IAClB;IAEA,IAAIV,mBAAmBD,aAAa;QAClCU,WAAWC,IAAI,CACb;IAEJ;IAEA,sBAAsB;IACtBD,WAAWC,IAAI,CACb,IACA,2CACA;IAGF,MAAMC,UAAUF,WAAWP,IAAI,CAACC,OAAOA;IAEvC,+CAA+C;IAC/C,IAAIE,mBAAmBM,SAAS;QAC9B;IACF;IACA,MAAMhB,GAAGiB,SAAS,CAACX,qBAAqBU;AAC1C"}