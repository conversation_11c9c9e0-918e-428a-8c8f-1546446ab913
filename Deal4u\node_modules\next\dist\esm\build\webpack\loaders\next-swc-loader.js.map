{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "isReactServerLayer", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "swcOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "pitch", "callback", "async", "process", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "loaderSpan", "currentTraceSpan", "addDependency", "r", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err", "raw"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAGA,SAASA,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AAiBvC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAgCMC,0BACZA,2BAESA;IAjCvB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,kBAAkB,EACnB,GAAGX;IACJ,MAAMY,aAAad,SAASe,UAAU,CAACT;IACvC,MAAMU,2BAA2BvB,KAAKwB,QAAQ,CAACZ,SAASL;IAExD,MAAMkB,aAAa1B,oBAAoB;QACrCc;QACAC;QACAP;QACAI;QACAU;QACAK,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3BZ;QACAa,iBAAiB,EAAEtB,8BAAAA,WAAYsB,iBAAiB;QAChDC,sBAAsB,EAAEvB,+BAAAA,2BAAAA,WAAYwB,YAAY,qBAAxBxB,yBAA0BuB,sBAAsB;QACxEE,UAAU,EAAEzB,+BAAAA,4BAAAA,WAAYwB,YAAY,qBAAxBxB,0BAA0ByB,UAAU;QAChDC,eAAe,EAAE1B,8BAAAA,WAAY2B,QAAQ;QACrCC,mBAAmB,EAAE5B,+BAAAA,4BAAAA,WAAYwB,YAAY,qBAAxBxB,0BAA0B4B,mBAAmB;QAClElB;QACAC;QACAC;QACAK;QACAJ;QACAC;IACF;IAEA,MAAMe,sBAAsB;QAC1B,GAAGV,UAAU;QACblB;QACAF,gBAAgBA,iBAAiB+B,KAAKC,SAAS,CAAChC,kBAAkBiC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBnC;IAClB;IAEA,IAAI,CAAC4B,oBAAoB9B,cAAc,EAAE;QACvC,OAAO8B,oBAAoB9B,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACsB,IAAI,IACTQ,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAAC7C,SAAS,IACjCqC,oBAAoBQ,GAAG,CAAC7C,SAAS,CAAC8C,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAAC7C,SAAS,CAAC8C,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAAC7C,SAAS,CAAC8C,KAAK,CAAClB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMsB,UAAU9C,YAAY+C,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BrD,UAAUM,QAAe+B,qBAAqBiB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOnB,KAAKoB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGvB,KAAKoB,KAAK,CAACH,OAAOM,GAAG,IAAIrB;aAAU;QACvE;AAEJ;AAEA,MAAMsB,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAACC,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACN,eAAeO,IAAI,CAAC,IAAI,CAAC3D,YAAY,KACtC,IAAI,CAAC4D,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CrE,WAAW,IAAI,CAACO,YAAY,KAC5B,CAAE,MAAMX,UACR;YACA,MAAM0E,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;YACpD,IAAI,CAACuB,aAAa,CAAC,IAAI,CAACjE,YAAY;YACpC,OAAO+D,WAAWpB,YAAY,CAAC,IAC7BjD,gBAAgB8C,IAAI,CAAC,IAAI,EAAEuB;QAE/B;IACF,CAAA,IAAKnB,IAAI,CAAC,CAACsB;QACT,IAAIA,GAAG,OAAOZ,SAAS,SAASY;QAChCZ;IACF,GAAGA;AACL;AAEA,eAAe,SAASa,UAEtBC,WAAmB,EACnBvE,cAAmB;IAEnB,MAAMkE,aAAa,IAAI,CAACC,gBAAgB,CAACtB,UAAU,CAAC;IACpD,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3BQ,WACGpB,YAAY,CAAC,IACZjD,gBAAgB8C,IAAI,CAAC,IAAI,EAAEuB,YAAYK,aAAavE,iBAErD+C,IAAI,CACH,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxChB,SAAS,MAAMe,mBAAmBC,mBAAmBzE;IACvD,GACA,CAAC0E;QACCjB,SAASiB;IACX;AAEN;AAEA,oCAAoC;AACpC,OAAO,MAAMC,MAAM,KAAI"}