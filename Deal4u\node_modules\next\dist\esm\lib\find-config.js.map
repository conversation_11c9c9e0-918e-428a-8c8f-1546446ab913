{"version": 3, "sources": ["../../src/lib/find-config.ts"], "names": ["findUp", "fs", "JSON5", "findConfigPath", "dir", "key", "cwd", "findConfig", "directory", "_returnFile", "packageJsonPath", "packageJson", "require", "filePath", "endsWith", "fileContents", "readFileSync", "parse"], "mappings": "AAAA,OAAOA,YAAY,6BAA4B;AAC/C,OAAOC,QAAQ,KAAI;AACnB,OAAOC,WAAW,2BAA0B;AAM5C,OAAO,SAASC,eACdC,GAAW,EACXC,GAAW;IAEX,4EAA4E;IAC5E,mBAAmB;IACnB,OAAOL,OACL;QACE,CAAC,CAAC,EAAEK,IAAI,OAAO,CAAC;QAChB,CAAC,EAAEA,IAAI,YAAY,CAAC;QACpB,CAAC,CAAC,EAAEA,IAAI,KAAK,CAAC;QACd,CAAC,EAAEA,IAAI,UAAU,CAAC;QAClB,CAAC,EAAEA,IAAI,WAAW,CAAC;KACpB,EACD;QACEC,KAAKF;IACP;AAEJ;AAEA,6EAA6E;AAC7E,4EAA4E;AAC5E,+CAA+C;AAC/C,OAAO,eAAeG,WACpBC,SAAiB,EACjBH,GAAW,EACXI,WAAqB;IAErB,oEAAoE;IACpE,MAAMC,kBAAkB,MAAMV,OAAO,gBAAgB;QAAEM,KAAKE;IAAU;IACtE,IAAIE,iBAAiB;QACnB,MAAMC,cAAcC,QAAQF;QAC5B,IAAIC,WAAW,CAACN,IAAI,IAAI,QAAQ,OAAOM,WAAW,CAACN,IAAI,KAAK,UAAU;YACpE,OAAOM,WAAW,CAACN,IAAI;QACzB;IACF;IAEA,MAAMQ,WAAW,MAAMV,eAAeK,WAAWH;IAEjD,IAAIQ,UAAU;QACZ,IAAIA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC,SAAS;YACzD,OAAOF,QAAQC;QACjB;QAEA,sEAAsE;QACtE,kEAAkE;QAClE,MAAME,eAAed,GAAGe,YAAY,CAACH,UAAU;QAC/C,OAAOX,MAAMe,KAAK,CAACF;IACrB;IAEA,OAAO;AACT"}