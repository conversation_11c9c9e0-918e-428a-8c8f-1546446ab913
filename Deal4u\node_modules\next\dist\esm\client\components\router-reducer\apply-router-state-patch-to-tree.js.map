{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts"], "names": ["matchSegment", "applyPatch", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "newParallelRoutes", "key", "isInPatchTreeParallelRoutes", "tree", "applyRouterStatePatchToTree", "flightSegmentPath", "flightRouterState", "treePatch", "segment", "parallelRoutes", "isRootLayout", "length", "currentSegment", "parallelRouteKey", "lastSegment", "parallelRoutePatch", "slice"], "mappings": "AAIA,SAASA,YAAY,QAAQ,oBAAmB;AAEhD;;CAEC,GACD,SAASC,WACPC,WAA8B,EAC9BC,SAA4B;IAE5B,MAAM,CAACC,gBAAgBC,sBAAsB,GAAGH;IAChD,MAAM,CAACI,cAAcC,oBAAoB,GAAGJ;IAE5C,gGAAgG;IAChG,iFAAiF;IACjF,IAAIG,iBAAiB,iBAAiBF,mBAAmB,eAAe;QACtE,OAAOF;IACT;IAEA,IAAIF,aAAaI,gBAAgBE,eAAe;QAC9C,MAAME,oBAA0C,CAAC;QACjD,IAAK,MAAMC,OAAOJ,sBAAuB;YACvC,MAAMK,8BACJ,OAAOH,mBAAmB,CAACE,IAAI,KAAK;YACtC,IAAIC,6BAA6B;gBAC/BF,iBAAiB,CAACC,IAAI,GAAGR,WACvBI,qBAAqB,CAACI,IAAI,EAC1BF,mBAAmB,CAACE,IAAI;YAE5B,OAAO;gBACLD,iBAAiB,CAACC,IAAI,GAAGJ,qBAAqB,CAACI,IAAI;YACrD;QACF;QAEA,IAAK,MAAMA,OAAOF,oBAAqB;YACrC,IAAIC,iBAAiB,CAACC,IAAI,EAAE;gBAC1B;YACF;YAEAD,iBAAiB,CAACC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;QACnD;QAEA,MAAME,OAA0B;YAACP;YAAgBI;SAAkB;QAEnE,IAAIN,WAAW,CAAC,EAAE,EAAE;YAClBS,IAAI,CAAC,EAAE,GAAGT,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBS,IAAI,CAAC,EAAE,GAAGT,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBS,IAAI,CAAC,EAAE,GAAGT,WAAW,CAAC,EAAE;QAC1B;QAEA,OAAOS;IACT;IAEA,OAAOR;AACT;AAEA;;CAEC,GACD,OAAO,SAASS,4BACdC,iBAAoC,EACpCC,iBAAoC,EACpCC,SAA4B;IAE5B,MAAM,CAACC,SAASC,oBAAoBC,aAAa,GAAGJ;IAEpD,eAAe;IACf,IAAID,kBAAkBM,MAAM,KAAK,GAAG;QAClC,MAAMR,OAA0BV,WAAWa,mBAAmBC;QAE9D,OAAOJ;IACT;IAEA,MAAM,CAACS,gBAAgBC,iBAAiB,GAAGR;IAE3C,iGAAiG;IACjG,IAAI,CAACb,aAAaoB,gBAAgBJ,UAAU;QAC1C,OAAO;IACT;IAEA,MAAMM,cAAcT,kBAAkBM,MAAM,KAAK;IAEjD,IAAII;IACJ,IAAID,aAAa;QACfC,qBAAqBtB,WAAWgB,cAAc,CAACI,iBAAiB,EAAEN;IACpE,OAAO;QACLQ,qBAAqBX,4BACnBC,kBAAkBW,KAAK,CAAC,IACxBP,cAAc,CAACI,iBAAiB,EAChCN;QAGF,IAAIQ,uBAAuB,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,MAAMZ,OAA0B;QAC9BE,iBAAiB,CAAC,EAAE;QACpB;YACE,GAAGI,cAAc;YACjB,CAACI,iBAAiB,EAAEE;QACtB;KACD;IAED,qCAAqC;IACrC,IAAIL,cAAc;QAChBP,IAAI,CAAC,EAAE,GAAG;IACZ;IAEA,OAAOA;AACT"}