{"version": 3, "sources": ["../../../src/client/components/not-found-boundary.tsx"], "names": ["React", "usePathname", "NotFoundErrorBoundary", "Component", "getDerivedStateFromError", "error", "digest", "notFoundTriggered", "getDerivedStateFromProps", "props", "state", "pathname", "previousPathname", "render", "meta", "name", "content", "process", "env", "NODE_ENV", "notFoundStyles", "notFound", "children", "constructor", "asNotFound", "NotFoundBoundary"], "mappings": "AAAA;AAEA,OAAOA,WAAW,QAAO;AACzB,SAASC,WAAW,QAAQ,eAAc;AAkB1C,MAAMC,8BAA8BF,MAAMG,SAAS;IAYjD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,IAAIA,CAAAA,yBAAAA,MAAOC,MAAM,MAAK,kBAAkB;YACtC,OAAO;gBAAEC,mBAAmB;YAAK;QACnC;QACA,mCAAmC;QACnC,MAAMF;IACR;IAEA,OAAOG,yBACLC,KAAiC,EACjCC,KAAiC,EACE;QACnC;;;;;KAKC,GACD,IAAID,MAAME,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMH,iBAAiB,EAAE;YACxE,OAAO;gBACLA,mBAAmB;gBACnBK,kBAAkBH,MAAME,QAAQ;YAClC;QACF;QACA,OAAO;YACLJ,mBAAmBG,MAAMH,iBAAiB;YAC1CK,kBAAkBH,MAAME,QAAQ;QAClC;IACF;IAEAE,SAAS;QACP,IAAI,IAAI,CAACH,KAAK,CAACH,iBAAiB,EAAE;YAChC,qBACE,wDACE,oBAACO;gBAAKC,MAAK;gBAASC,SAAQ;gBAC3BC,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,oBAACL;gBAAKC,MAAK;gBAAaC,SAAQ;gBAEjC,IAAI,CAACP,KAAK,CAACW,cAAc,EACzB,IAAI,CAACX,KAAK,CAACY,QAAQ;QAG1B;QAEA,OAAO,IAAI,CAACZ,KAAK,CAACa,QAAQ;IAC5B;IArDAC,YAAYd,KAAiC,CAAE;QAC7C,KAAK,CAACA;QACN,IAAI,CAACC,KAAK,GAAG;YACXH,mBAAmB,CAAC,CAACE,MAAMe,UAAU;YACrCZ,kBAAkBH,MAAME,QAAQ;QAClC;IACF;AAgDF;AAEA,OAAO,SAASc,iBAAiB,KAKT;IALS,IAAA,EAC/BJ,QAAQ,EACRD,cAAc,EACdI,UAAU,EACVF,QAAQ,EACc,GALS;IAM/B,MAAMX,WAAWV;IACjB,OAAOoB,yBACL,oBAACnB;QACCS,UAAUA;QACVU,UAAUA;QACVD,gBAAgBA;QAChBI,YAAYA;OAEXF,0BAGH,0CAAGA;AAEP"}