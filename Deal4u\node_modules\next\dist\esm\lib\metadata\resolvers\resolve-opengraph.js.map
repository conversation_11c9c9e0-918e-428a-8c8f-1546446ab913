{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "names": ["resolveAsArrayOrUndefined", "getSocialImageFallbackMetadataBase", "isStringOrURL", "resolveUrl", "resolveAbsoluteUrlWithPathname", "resolveTitle", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "resolveImages", "images", "metadataBase", "resolvedImages", "nonNullableImages", "item", "isItemUrl", "inputUrl", "url", "push", "getFieldsByOgType", "ogType", "resolveOpenGraph", "openGraph", "pathname", "titleTemplate", "resolveProps", "target", "og", "type", "undefined", "keys", "k", "key", "value", "arrayValue", "imageMetadataBase", "resolved", "title", "TwitterBasicInfoKeys", "resolveTwitter", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": "AAWA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SACEC,kCAAkC,EAClCC,aAAa,EACbC,UAAU,EACVC,8BAA8B,QACzB,gBAAe;AACtB,SAASC,YAAY,QAAQ,kBAAiB;AAE9C,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAUA,OAAO,SAASC,cACdC,MAA+C,EAC/CC,YAA8C;IAI9C,MAAMC,iBAAiBhB,0BAA0Bc;IACjD,IAAI,CAACE,gBAAgB,OAAOA;IAE5B,MAAMC,oBAAoB,EAAE;IAC5B,KAAK,MAAMC,QAAQF,eAAgB;QACjC,IAAI,CAACE,MAAM;QACX,MAAMC,YAAYjB,cAAcgB;QAChC,MAAME,WAAWD,YAAYD,OAAOA,KAAKG,GAAG;QAC5C,IAAI,CAACD,UAAU;QAEfH,kBAAkBK,IAAI,CACpBH,YACI;YACEE,KAAKlB,WAAWe,MAAMH;QACxB,IACA;YACE,GAAGG,IAAI;YACP,8BAA8B;YAC9BG,KAAKlB,WAAWe,KAAKG,GAAG,EAAEN;QAC5B;IAER;IAEA,OAAOE;AACT;AAEA,SAASM,kBAAkBC,MAAiC;IAC1D,OAAQA;QACN,KAAK;QACL,KAAK;YACH,OAAOlB,aAAaC,OAAO;QAC7B,KAAK;QACL,KAAK;YACH,OAAOD,aAAaE,IAAI;QAC1B,KAAK;YACH,OAAOF,aAAaG,QAAQ;QAC9B,KAAK;YACH,OAAOH,aAAaI,KAAK;QAC3B,KAAK;QACL,KAAK;YACH,OAAOJ,aAAaK,KAAK;QAC3B;YACE,OAAOL,aAAaM,KAAK;IAC7B;AACF;AAEA,OAAO,MAAMa,mBAGT,CAACC,WAAWX,cAAc,EAAEY,QAAQ,EAAE,EAAEC;IAC1C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMP,SAASO,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAGC;QAC9C,MAAMC,OAAOX,kBAAkBC;QAC/B,KAAK,MAAMW,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOL,MAAMK,QAAQ,OAAO;gBAC9B,MAAMC,QAAQN,EAAE,CAACK,IAAI;gBACrB,IAAIC,OAAO;oBACT,MAAMC,aAAatC,0BAA0BqC;oBAE3CP,MAAc,CAACM,IAAI,GAAGE;gBAC1B;YACF;QACF;QAEA,MAAMC,oBAAoBtC,mCAAmCc;QAC7De,OAAOhB,MAAM,GAAGD,cAAckB,GAAGjB,MAAM,EAAEyB;IAC3C;IAEA,MAAMC,WAAW;QACf,GAAGd,SAAS;QACZe,OAAOpC,aAAaqB,UAAUe,KAAK,EAAEb;IACvC;IACAC,aAAaW,UAAUd;IAEvBc,SAASnB,GAAG,GAAGK,UAAUL,GAAG,GACxBjB,+BAA+BsB,UAAUL,GAAG,EAAEN,cAAcY,YAC5D;IAEJ,OAAOa;AACT,EAAC;AAED,MAAME,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,MAAMC,iBAGT,CAACC,SAAS7B,cAAca;QAaVY;IAZhB,IAAI,CAACI,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAGZ;IAC9C,MAAMO,WAAW;QACf,GAAGI,OAAO;QACVH,OAAOpC,aAAauC,QAAQH,KAAK,EAAEb;IACrC;IACA,KAAK,MAAMkB,WAAWJ,qBAAsB;QAC1CF,QAAQ,CAACM,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IACA,MAAMP,oBAAoBtC,mCAAmCc;IAC7DyB,SAAS1B,MAAM,GAAGD,cAAc+B,QAAQ9B,MAAM,EAAEyB;IAEhDM,OAAOA,QAASL,CAAAA,EAAAA,mBAAAA,SAAS1B,MAAM,qBAAf0B,iBAAiBO,MAAM,IAAG,wBAAwB,SAAQ;IAC1EP,SAASK,IAAI,GAAGA;IAEhB,IAAI,UAAUL,UAAU;QACtB,OAAQA,SAASK,IAAI;YACnB,KAAK;gBAAU;oBACbL,SAASQ,OAAO,GAAGhD,0BAA0BwC,SAASQ,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVR,SAASS,GAAG,GAAGT,SAASS,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOT;AACT,EAAC"}