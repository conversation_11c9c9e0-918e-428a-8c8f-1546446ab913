{"version": 3, "sources": ["../../src/client/add-base-path.ts"], "names": ["addPathPrefix", "normalizePathTrailingSlash", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "addBasePath", "path", "required", "__NEXT_MANUAL_CLIENT_BASE_PATH"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6CAA4C;AAC1E,SAASC,0BAA0B,QAAQ,6BAA4B;AAEvE,MAAMC,WAAW,AAACC,QAAQC,GAAG,CAACC,sBAAsB,IAAe;AAEnE,OAAO,SAASC,YAAYC,IAAY,EAAEC,QAAkB;IAC1D,OAAOP,2BACLE,QAAQC,GAAG,CAACK,8BAA8B,IAAI,CAACD,WAC3CD,OACAP,cAAcO,MAAML;AAE5B"}