{"version": 3, "sources": ["../../../src/client/components/use-reducer-with-devtools.ts"], "names": ["useRef", "useReducer", "useEffect", "useCallback", "normalizeRouterState", "val", "Map", "obj", "key", "value", "entries", "$$typeof", "toString", "_bundlerConfig", "hasOwnProperty", "Array", "isArray", "map", "devToolReducer", "fn", "ref", "state", "action", "res", "current", "send", "useReducerWithReduxDevtoolsNoop", "initialState", "dispatch", "useReducerWithReduxDevtoolsImpl", "devtoolsConnectionRef", "enabledRef", "undefined", "window", "__REDUX_DEVTOOLS_EXTENSION__", "connect", "instanceId", "name", "init", "sync", "type", "useReducerWithReduxDevtools"], "mappings": "AAOA,SAASA,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,QAAO;AAElE,SAASC,qBAAqBC,GAAQ;IACpC,IAAIA,eAAeC,KAAK;QACtB,MAAMC,MAA8B,CAAC;QACrC,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIJ,IAAIK,OAAO,GAAI;YACxC,IAAI,OAAOD,UAAU,YAAY;gBAC/BF,GAAG,CAACC,IAAI,GAAG;gBACX;YACF;YACA,IAAI,OAAOC,UAAU,YAAYA,UAAU,MAAM;gBAC/C,IAAIA,MAAME,QAAQ,EAAE;oBAClBJ,GAAG,CAACC,IAAI,GAAGC,MAAME,QAAQ,CAACC,QAAQ;oBAClC;gBACF;gBACA,IAAIH,MAAMI,cAAc,EAAE;oBACxBN,GAAG,CAACC,IAAI,GAAG;oBACX;gBACF;YACF;YACAD,GAAG,CAACC,IAAI,GAAGJ,qBAAqBK;QAClC;QACA,OAAOF;IACT;IAEA,IAAI,OAAOF,QAAQ,YAAYA,QAAQ,MAAM;QAC3C,MAAME,MAA8B,CAAC;QACrC,IAAK,MAAMC,OAAOH,IAAK;YACrB,MAAMI,QAAQJ,GAAG,CAACG,IAAI;YACtB,IAAI,OAAOC,UAAU,YAAY;gBAC/BF,GAAG,CAACC,IAAI,GAAG;gBACX;YACF;YACA,IAAI,OAAOC,UAAU,YAAYA,UAAU,MAAM;gBAC/C,IAAIA,MAAME,QAAQ,EAAE;oBAClBJ,GAAG,CAACC,IAAI,GAAGC,MAAME,QAAQ,CAACC,QAAQ;oBAClC;gBACF;gBACA,IAAIH,MAAMK,cAAc,CAAC,mBAAmB;oBAC1CP,GAAG,CAACC,IAAI,GAAG;oBACX;gBACF;YACF;YAEAD,GAAG,CAACC,IAAI,GAAGJ,qBAAqBK;QAClC;QACA,OAAOF;IACT;IAEA,IAAIQ,MAAMC,OAAO,CAACX,MAAM;QACtB,OAAOA,IAAIY,GAAG,CAACb;IACjB;IAEA,OAAOC;AACT;AA6BA,SAASa,eACPC,EAAkB,EAClBC,GAAwD;IAExD,OAAO,CACLC,OACAC;QAEA,MAAMC,MAAMJ,GAAGE,OAAOC;QACtB,IAAIF,IAAII,OAAO,EAAE;YACfJ,IAAII,OAAO,CAACC,IAAI,CAACH,QAAQlB,qBAAqBmB;QAChD;QACA,OAAOA;IACT;AACF;AAEA,SAASG,gCACPP,EAAkB,EAClBQ,YAAwC;IAMxC,MAAM,CAACN,OAAOO,SAAS,GAAG3B,WAAWkB,IAAIQ;IAEzC,OAAO;QAACN;QAAOO;QAAU,KAAO;KAAE;AACpC;AAEA,SAASC,gCACPV,EAAkB,EAClBQ,YAAwC;IAMxC,MAAMG,wBAAwB9B;IAC9B,MAAM+B,aAAa/B;IAEnBE,UAAU;QACR,IAAI4B,sBAAsBN,OAAO,IAAIO,WAAWP,OAAO,KAAK,OAAO;YACjE;QACF;QAEA,IACEO,WAAWP,OAAO,KAAKQ,aACvB,OAAOC,OAAOC,4BAA4B,KAAK,aAC/C;YACAH,WAAWP,OAAO,GAAG;YACrB;QACF;QAEAM,sBAAsBN,OAAO,GAAGS,OAAOC,4BAA4B,CAACC,OAAO,CACzE;YACEC,YAAY;YACZC,MAAM;QACR;QAEF,IAAIP,sBAAsBN,OAAO,EAAE;YACjCM,sBAAsBN,OAAO,CAACc,IAAI,CAAClC,qBAAqBuB;QAC1D;QAEA,OAAO;YACLG,sBAAsBN,OAAO,GAAGQ;QAClC;IACF,GAAG;QAACL;KAAa;IAEjB,MAAM,CAACN,OAAOO,SAAS,GAAG3B,WACxBiB,eAAe,eAAe,GAAGC,GAAG,GAAG,KAAIW,wBAC3CH;IAGF,MAAMY,OAAOpC,YAAY;QACvB,IAAI2B,sBAAsBN,OAAO,EAAE;YACjCM,sBAAsBN,OAAO,CAACC,IAAI,CAChC;gBAAEe,MAAM;YAAc,GACtBpC,qBAAqBiB;QAEzB;IACF,GAAG;QAACA;KAAM;IACV,OAAO;QAACA;QAAOO;QAAUW;KAAK;AAChC;AAEA,OAAO,MAAME,8BACX,OAAOR,WAAW,cACdJ,kCACAH,gCAA+B"}