{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-edge-function-loader.ts"], "names": ["getModuleBuildInfo", "stringifyRequest", "nextEdgeFunctionLoader", "absolutePagePath", "page", "rootDir", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "stringifiedPagePath", "buildInfo", "_module", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "route", "nextEdgeApiFunction", "stringify"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,gBAAgB,QAAQ,uBAAsB;AAWvD,MAAMC,yBACJ,SAASA;IACP,MAAM,EACJC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAA8B,IAAI,CAACC,UAAU;IAC9C,MAAMC,sBAAsBT,iBAAiB,IAAI,EAAEE;IACnD,MAAMQ,YAAYX,mBAAmB,IAAI,CAACY,OAAO;IACjD,MAAML,mBAAqCM,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACR,wBAAwB,UAAUS,QAAQ;IAExDN,UAAUO,KAAK,GAAG;QAChBd,MAAMA,QAAQ;QACdD;QACAG;QACAC;IACF;IACAI,UAAUQ,mBAAmB,GAAG;QAC9Bf,MAAMA,QAAQ;IAChB;IACAO,UAAUN,OAAO,GAAGA;IAEpB,OAAO,CAAC;;;;;4BAKgB,EAAEK,oBAAoB;;;mDAGC,EAAEN,KAAK;;;;;;;oBAOtC,EAAES,KAAKO,SAAS,CAAChB,MAAM;;;;IAIvC,CAAC;AACH;AAEF,eAAeF,uBAAsB"}